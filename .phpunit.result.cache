{"version": 1, "defects": {"Tests\\Feature\\BusinessTest::test_authenticated_user_can_access_dashboard": 7, "Tests\\Feature\\BusinessTest::test_business_can_be_updated": 8, "Tests\\Feature\\BusinessTest::test_business_can_be_deleted": 8, "Tests\\Feature\\ApiIntegrationTest::test_taqnyat_otp_verification_requires_valid_session": 7, "Tests\\Feature\\ApiIntegrationTest::test_360dialog_token_update_with_valid_token": 7, "Tests\\Feature\\ApiIntegrationTest::test_phone_number_formatting_in_requests": 7, "Tests\\Feature\\ApiIntegrationTest::test_bearer_token_validation_rejects_invalid_characters": 7, "Tests\\Feature\\ApiIntegrationTest::test_otp_validation_rejects_non_numeric": 7}, "times": {"Tests\\Feature\\BusinessTest::test_homepage_redirects_to_login": 0.035, "Tests\\Feature\\BusinessTest::test_authenticated_user_can_access_dashboard": 0.048, "Tests\\Feature\\BusinessTest::test_authenticated_user_can_view_businesses": 0.004, "Tests\\Feature\\BusinessTest::test_authenticated_user_can_create_business": 0.017, "Tests\\Feature\\BusinessTest::test_business_creation_requires_name": 0.004, "Tests\\Feature\\BusinessTest::test_business_can_be_updated": 0.016, "Tests\\Feature\\BusinessTest::test_business_can_be_deleted": 0.006, "Tests\\Feature\\ApiIntegrationTest::test_taqnyat_otp_request_requires_authentication": 0.041, "Tests\\Feature\\ApiIntegrationTest::test_taqnyat_otp_request_fails_without_user_phone": 0.015, "Tests\\Feature\\ApiIntegrationTest::test_taqnyat_otp_request_with_user_phone_number": 0.545, "Tests\\Feature\\ApiIntegrationTest::test_taqnyat_otp_verification_requires_valid_session": 0.015, "Tests\\Feature\\ApiIntegrationTest::test_taqnyat_otp_verification_validates_otp_format": 0.002, "Tests\\Feature\\ApiIntegrationTest::test_taqnyat_demo_otp_verification_works": 0.975, "Tests\\Feature\\ApiIntegrationTest::test_taqnyat_auth_status_when_not_authenticated": 0.002, "Tests\\Feature\\ApiIntegrationTest::test_360dialog_token_update_requires_authentication": 0.002, "Tests\\Feature\\ApiIntegrationTest::test_360dialog_token_update_validates_token_format": 0.004, "Tests\\Feature\\ApiIntegrationTest::test_360dialog_token_update_with_valid_token": 0.003, "Tests\\Feature\\ApiIntegrationTest::test_360dialog_auth_status_when_not_authenticated": 0.002, "Tests\\Feature\\ApiIntegrationTest::test_360dialog_test_message_validates_phone_and_message": 0.002, "Tests\\Feature\\ApiIntegrationTest::test_taqnyat_service_can_be_instantiated": 0.001, "Tests\\Feature\\ApiIntegrationTest::test_dialog360_service_can_be_instantiated": 0, "Tests\\Feature\\ApiIntegrationTest::test_phone_number_formatting_in_requests": 0.449, "Tests\\Feature\\ApiIntegrationTest::test_bearer_token_validation_rejects_invalid_characters": 0.003, "Tests\\Feature\\ApiIntegrationTest::test_otp_validation_rejects_non_numeric": 0.002, "Tests\\Feature\\ApiIntegrationTest::test_user_can_update_phone_number": 0.003, "Tests\\Feature\\ApiIntegrationTest::test_phone_update_validates_format": 0.002, "Tests\\Feature\\ApiIntegrationTest::test_phone_update_prevents_duplicates": 0.002, "Tests\\Feature\\ApiIntegrationTest::test_jwt_token_validation_rejects_invalid_structure": 0.002, "Tests\\Feature\\ApiIntegrationTest::test_jwt_token_validation_rejects_empty_parts": 0.002, "Tests\\Feature\\ApiIntegrationTest::test_jwt_token_validation_accepts_valid_jwt": 0.003, "Tests\\Feature\\ApiIntegrationTest::test_jwt_token_storage_and_retrieval": 0.004, "Tests\\Feature\\ApiIntegrationTest::test_bearer_token_validation_rejects_short_tokens": 0.002}}