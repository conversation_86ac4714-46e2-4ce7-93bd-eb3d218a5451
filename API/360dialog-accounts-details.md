curl 'https://hub.360dialog.io/v1/partners/GbM78BPA/channels?offset=0&limit=50&sort=-modified_at&filters={}' \
-H 'Host: hub.360dialog.io' \
-H 'Connection: keep-alive' \
-H 'sec-ch-ua-platform: "macOS"' \
-H 'Authorization: Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36' \
-H 'Accept: application/json, text/plain, */*' \
-H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
-H 'sec-ch-ua-mobile: ?0' \
-H 'Origin: https://hub.360dialog.com' \
-H 'Sec-Fetch-Site: cross-site' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Referer: https://hub.360dialog.com/' \
-H 'Accept-Language: en-US,en;q=0.9' \

======================================================================================================

expected results 

{
  "count": 5,
  "filters": {},
  "limit": 5,
  "offset": 0,
  "partner_channels": [
    {
      "account_mode": "live",
      "auto_recharge_enabled": false,
      "auto_update_recharge_settings": false,
      "billing_started_at": null,
      "business_capability_update": null,
      "cancelled_at": null,
      "channel_origin": "other_bsp_migration",
      "client": {
        "client_allowed_to_add_more_channels": true,
        "client_connected_channels_count": 1,
        "client_requested_channels_count": 0,
        "contact_info": {
          "email": "<EMAIL>"
        },
        "contact_user": null,
        "created_at": "2025-01-10T11:02:00Z",
        "created_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "enabled_for_chat_support": true,
        "fb_business_id": null,
        "id": "TQiJHPKPCL",
        "max_channels": 10,
        "meta_info": null,
        "modified_at": "2025-01-10T11:02:00Z",
        "modified_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "name": "الجمعية الخيرية لتحفيظ القران الكريم بالمنطقة الشرقية",
        "organisation": null,
        "partner_payload": null,
        "status": "active",
        "suspicious": false
      },
      "client_id": "TQiJHPKPCL",
      "consents": {
        "url": "https://hub.360dialog.com/lp/submit-waba/GbM78BPA/channel/9FJAbiCH?token=0e400cb6e36b5c999f8d40bbd77e2a693ce1b21dE7JzlpfZVxcrvkrHztyF5SzHWtX0LlXK"
      },
      "consents_signed_at": null,
      "created_at": "2025-01-10T11:10:49Z",
      "created_by": {
        "user_id": "bfeD1DU",
        "user_name": "الجمعية الخيرية لتحفيظ القران الكريم بالمنطقة الشرقية"
      },
      "current_balance": -1.0,
      "current_limit": "TIER_100K",
      "current_quality_rating": "High",
      "current_quality_update_event": "CONNECTED",
      "external_id": "571770922676292",
      "has_inbox": false,
      "id": "9FJAbiCH",
      "integration": {
        "app_id": "171231",
        "hosting_platform_type": "meta_cloud_api",
        "id": "TnLee9",
        "premium": false,
        "state": "running",
        "token": "9544127d7d32169679dd3402ddab132eefcdb02beSf6KCybdgxWy664viDQVOiumGepzWzL"
      },
      "integration_id": "TnLee9",
      "is_allowed_messaging_with_negative_balance": true,
      "is_allowed_to_send_template_message": true,
      "is_fraud": false,
      "is_migrated": true,
      "is_oba": false,
      "last_downgrade_at": null,
      "last_upgrade_at": null,
      "meta_status": "live",
      "modified_at": "2025-06-28T20:03:36Z",
      "modified_by": {
        "user_id": "system",
        "user_name": "system"
      },
      "on_support": false,
      "partner_id": "GbM78BPA",
      "payment_connection": {
        "approved_at": null,
        "channel_id": "9FJAbiCH",
        "client_id": "TQiJHPKPCL",
        "created_at": "2025-01-13T12:11:12Z",
        "created_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "discount": 0.0,
        "finish_date": "2025-03-31T22:14:05Z",
        "id": "t5IIQRPC",
        "modified_at": "2025-03-31T22:14:05Z",
        "modified_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "partner_id": "GbM78BPA",
        "products": {
          "hosting": true
        },
        "start_date": "2025-01-13T12:11:12Z",
        "type": "partner"
      },
      "profile_info": {},
      "project": {
        "api_user_email": null,
        "app_id": "171231",
        "client_id": "TQiJHPKPCL",
        "created_at": "2025-01-10T11:10:49Z",
        "created_by": {
          "user_id": "bfeD1DU",
          "user_name": "الجمعية الخيرية لتحفيظ القران الكريم بالمنطقة الشرقية"
        },
        "id": "muzfhTPR",
        "inbox": false,
        "license_model": "cloud",
        "modified_at": "2025-01-10T11:10:49Z",
        "modified_by": {
          "user_id": "bfeD1DU",
          "user_name": "الجمعية الخيرية لتحفيظ القران الكريم بالمنطقة الشرقية"
        },
        "name": "الجمعية الخيرية لتحفيظ القران الكريم بالمنطقة الشرقية - FB None",
        "partner_id": "GbM78BPA",
        "status": "active",
        "webhook": null
      },
      "project_id": "muzfhTPR",
      "recharge_balance_threshold": 0.0,
      "recharge_top_up_amount": 0.0,
      "settings": {
        "feature_type": "only_waba_sharing",
        "internal_migration_data": {
          "source_channel_external_id": "571770922676292",
          "source_waba_external_id": "***************"
        },
        "tier": "regular"
      },
      "setup_info": {
        "certificate": "CpMBCk8InoKc27avrgMSBmVudDp3YSI22KrYrdmB2YrYuCDYp9mE2LTYsdmC2YrYqSAtINmB2LHYuSDYutix2Kgg2KfZhNiv2YXYp9mFUN/h47wGGkCdr283jVJb7MxIJQYqBte/UcL6HIJBDWW+FclIdOhvxz/TF54hEra6Omd0oVSgYCzXUl+AFlfeN6rEVUIybZ8NEi5tNi2/uOfmTeBEh7SeqW4hk1vs7VTC2MKnLz1OrTy1yzpddlsHPiWPgVzBZcli",
        "default_language": "en",
        "first_billing_started_at": "2025-01-13T12:11:12Z",
        "ivr": false,
        "phone_name": "تحفيظ الشرقية - فرع غرب الدمام",
        "phone_number": "************",
        "phone_number_country": "SA",
        "registration_error": null,
        "verification_method": "sms",
        "was_in_use": false
      },
      "status": "ready",
      "terminated_at": null,
      "type": "whatsapp",
      "version": 2,
      "waba_account": {
        "business_capability_update": null,
        "client_id": "TQiJHPKPCL",
        "consents": {},
        "created_at": "2025-01-28T10:13:03Z",
        "created_by": {
          "user_id": "bfeD1DU",
          "user_name": "الجمعية الخيرية لتحفيظ القران الكريم بالمنطقة الشرقية"
        },
        "debug_info": null,
        "external_id": "***************",
        "fb_account_status": "verified",
        "fb_business_id": "***************",
        "has_signed_consents": false,
        "id": "ZpRJdeWA",
        "modified_at": "2025-05-29T21:28:47Z",
        "modified_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "name": "تحفيظ الشرقية - فرع غرب الدمام",
        "namespace": "4d9239f1_c357_47c2_9d03_9248a1a92999",
        "on_behalf_of_business_info": {
          "id": "***************",
          "name": "تحفيظ القرآن بالشرقية",
          "status": "approved",
          "type": "SELF"
        },
        "partner_id": "GbM78BPA",
        "primary_funding_id": null,
        "status": "approved",
        "timezone_id": "76"
      },
      "waba_account_id": "ZpRJdeWA",
      "waba_policy_violations": [
        {
          "created_at": "2025-03-22T14:24:53Z",
          "created_by": {
            "user_id": "system",
            "user_name": "system"
          },
          "event": "ACCOUNT_VIOLATION",
          "expires_at": null,
          "id": "deQqbrWPV",
          "modified_at": "2025-03-22T14:24:53Z",
          "modified_by": {
            "user_id": "system",
            "user_name": "system"
          },
          "payload": {
            "business_verification_status": null,
            "event": "ACCOUNT_VIOLATION",
            "phone_number": null,
            "restriction_info": null,
            "time": "2025-03-22T14:24:51Z",
            "violation_info": {
              "violation_type": "SPAM"
            },
            "waba_account_id": "ZpRJdeWA"
          },
          "type": "SPAM",
          "waba_account_id": "ZpRJdeWA"
        },
        {
          "created_at": "2025-03-19T14:24:09Z",
          "created_by": {
            "user_id": "system",
            "user_name": "system"
          },
          "event": "ACCOUNT_VIOLATION",
          "expires_at": null,
          "id": "klbsZ8WPV",
          "modified_at": "2025-03-19T14:24:09Z",
          "modified_by": {
            "user_id": "system",
            "user_name": "system"
          },
          "payload": {
            "business_verification_status": null,
            "event": "ACCOUNT_VIOLATION",
            "phone_number": null,
            "restriction_info": null,
            "time": "2025-03-19T14:24:05Z",
            "violation_info": {
              "violation_type": "SPAM"
            },
            "waba_account_id": "ZpRJdeWA"
          },
          "type": "SPAM",
          "waba_account_id": "ZpRJdeWA"
        },
        {
          "created_at": "2025-03-28T14:25:58Z",
          "created_by": {
            "user_id": "system",
            "user_name": "system"
          },
          "event": "ACCOUNT_VIOLATION",
          "expires_at": null,
          "id": "DYahv3WPV",
          "modified_at": "2025-03-28T14:25:58Z",
          "modified_by": {
            "user_id": "system",
            "user_name": "system"
          },
          "payload": {
            "business_verification_status": null,
            "event": "ACCOUNT_VIOLATION",
            "phone_number": null,
            "restriction_info": null,
            "time": "2025-03-28T14:25:57Z",
            "violation_info": {
              "violation_type": "SPAM"
            },
            "waba_account_id": "ZpRJdeWA"
          },
          "type": "SPAM",
          "waba_account_id": "ZpRJdeWA"
        },
        {
          "created_at": "2025-03-28T14:25:58Z",
          "created_by": {
            "user_id": "system",
            "user_name": "system"
          },
          "event": "ACCOUNT_VIOLATION",
          "expires_at": null,
          "id": "3kLZUEWPV",
          "modified_at": "2025-03-28T14:25:58Z",
          "modified_by": {
            "user_id": "system",
            "user_name": "system"
          },
          "payload": {
            "business_verification_status": null,
            "event": "ACCOUNT_VIOLATION",
            "phone_number": null,
            "restriction_info": null,
            "time": "2025-03-28T14:25:57Z",
            "violation_info": {
              "violation_type": "SPAM"
            },
            "waba_account_id": "ZpRJdeWA"
          },
          "type": "SPAM",
          "waba_account_id": "ZpRJdeWA"
        },
        {
          "created_at": "2025-03-31T14:27:31Z",
          "created_by": {
            "user_id": "system",
            "user_name": "system"
          },
          "event": "ACCOUNT_VIOLATION",
          "expires_at": null,
          "id": "M0TGV7WPV",
          "modified_at": "2025-03-31T14:27:31Z",
          "modified_by": {
            "user_id": "system",
            "user_name": "system"
          },
          "payload": {
            "business_verification_status": null,
            "event": "ACCOUNT_VIOLATION",
            "phone_number": null,
            "restriction_info": null,
            "time": "2025-03-31T14:27:26Z",
            "violation_info": {
              "violation_type": "SPAM"
            },
            "waba_account_id": "ZpRJdeWA"
          },
          "type": "SPAM",
          "waba_account_id": "ZpRJdeWA"
        },
        {
          "created_at": "2025-03-25T14:25:50Z",
          "created_by": {
            "user_id": "system",
            "user_name": "system"
          },
          "event": "ACCOUNT_VIOLATION",
          "expires_at": null,
          "id": "F8E20rWPV",
          "modified_at": "2025-03-25T14:25:50Z",
          "modified_by": {
            "user_id": "system",
            "user_name": "system"
          },
          "payload": {
            "business_verification_status": null,
            "event": "ACCOUNT_VIOLATION",
            "phone_number": null,
            "restriction_info": null,
            "time": "2025-03-25T14:25:45Z",
            "violation_info": {
              "violation_type": "SPAM"
            },
            "waba_account_id": "ZpRJdeWA"
          },
          "type": "SPAM",
          "waba_account_id": "ZpRJdeWA"
        }
      ]
    },
    {
      "account_mode": "live",
      "auto_recharge_enabled": false,
      "auto_update_recharge_settings": false,
      "billing_started_at": null,
      "business_capability_update": null,
      "cancelled_at": null,
      "channel_origin": null,
      "client": {
        "client_allowed_to_add_more_channels": true,
        "client_connected_channels_count": 1,
        "client_requested_channels_count": 0,
        "contact_info": {
          "email": "<EMAIL>",
          "full_name": "Bahaa Qaddoura"
        },
        "contact_user": {},
        "created_at": "2022-01-20T10:24:21Z",
        "created_by": {
          "user_id": "system",
          "user_name": "System account"
        },
        "enabled_for_chat_support": true,
        "fb_business_id": "***************_shared",
        "id": "3eNMBBwMCL",
        "max_channels": 10,
        "meta_info": {
          "use_case": "Accepted changed Terms of Service (pricing changes 01.06.2023) on 16.05.2023 01:37 +03:00"
        },
        "modified_at": "2024-03-12T13:44:53Z",
        "modified_by": {
          "user_id": "TMHDYJU",
          "user_name": "Reza Ghasemi"
        },
        "name": "taqnyat Mobile information company LTD",
        "organisation": null,
        "partner_payload": null,
        "status": "active",
        "suspicious": false
      },
      "client_id": "3eNMBBwMCL",
      "consents": {
        "signed_at": "2022-01-20T10:24:21Z"
      },
      "consents_signed_at": "2022-01-20T10:24:21Z",
      "created_at": "2022-01-20T10:24:21Z",
      "created_by": {
        "user_id": "system",
        "user_name": "System account"
      },
      "current_balance": -1.0,
      "current_limit": "TIER_100K",
      "current_quality_rating": "High",
      "current_quality_update_event": "CONNECTED",
      "external_id": "****************",
      "has_inbox": false,
      "id": "fCFWoZCH",
      "integration": {
        "app_id": "42384",
        "hosting_platform_type": "meta_cloud_api",
        "id": "d0Rt42",
        "premium": false,
        "state": "running",
        "token": "4bbf81548fbc7b6e6f002add4856e339b16850f2rPeN3JGCqA7vQNpoHklc97pKfgAhUju3"
      },
      "integration_id": "d0Rt42",
      "is_allowed_messaging_with_negative_balance": true,
      "is_allowed_to_send_template_message": true,
      "is_fraud": false,
      "is_migrated": true,
      "is_oba": false,
      "last_downgrade_at": null,
      "last_upgrade_at": null,
      "meta_status": "live",
      "modified_at": "2025-06-28T20:03:29Z",
      "modified_by": {
        "user_id": "system",
        "user_name": "system"
      },
      "on_support": false,
      "partner_id": "GbM78BPA",
      "payment_connection": {
        "approved_at": null,
        "channel_id": "fCFWoZCH",
        "client_id": "3eNMBBwMCL",
        "created_at": "2023-07-31T20:18:53Z",
        "created_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "discount": 0.0,
        "finish_date": "2025-03-31T21:59:57Z",
        "id": "AQ5agyPC",
        "modified_at": "2025-03-31T21:59:57Z",
        "modified_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "partner_id": "GbM78BPA",
        "products": {
          "hosting": true
        },
        "start_date": "2023-08-01T00:00:00Z",
        "type": "partner"
      },
      "profile_info": {
        "about_text": "Boost your business with quick and easy integration using Taqnyat",
        "business_description": "We are experts on telecommunication industry and we know how service quality and security means to their businesses, so we are able to differentiate you from the competition and communicate your unique value proposition ",
        "business_vertical": "PROF_SERVICES",
        "contact_email": "<EMAIL>",
        "full_address": "Prince Mohammed bin Abdulaziz Road Jadeh",
        "picture_url": "https://pps.whatsapp.net/v/t61.24694-24/219843837_338440094796433_8601212938362779807_n.jpg?stp=dst-jpg_tt6&ccb=11-4&oh=01_Q5AaID6ma7kjOWGkLLd-0JX1qzdBWk55duO1d7RyNMsTBveu&oe=6755EEB0&_nc_sid=5e03e0&_nc_cat=107",
        "webpage_url": "https://www.taqnyat.sa/",
        "websites": [
          "https://www.taqnyat.sa/"
        ]
      },
      "project": {
        "api_user_email": null,
        "app_id": "42384",
        "client_id": "3eNMBBwMCL",
        "created_at": "2022-01-20T10:24:21Z",
        "created_by": {
          "user_id": "system",
          "user_name": "System account"
        },
        "id": "9tJH3tPR",
        "inbox": false,
        "license_model": "cloud",
        "modified_at": "2022-01-20T10:24:21Z",
        "modified_by": {
          "user_id": "system",
          "user_name": "System account"
        },
        "name": "taqnyat Mobile information company LTD - FB ***************",
        "partner_id": "GbM78BPA",
        "status": "active",
        "webhook": null
      },
      "project_id": "9tJH3tPR",
      "recharge_balance_threshold": 0.0,
      "recharge_top_up_amount": 0.0,
      "settings": {
        "tier": "regular"
      },
      "setup_info": {
        "certificate": "CmQKIAjj0NTYy9SiAhIGZW50OndhIgdUYXFueWF0UM/PvrMGGkAE1hvDQIWkF89S35BIzQHT2doMt9pfcO3NG8QH/7b0FMc6wb6YUsKNEJSLRyIsgUPBha6w4TVn41ZCn86oF+0MEi9tdQuBz5yq4/NasrueqmQrlVrl4FjF9AWJAz/7ixz87W4WXb/Oa496yP/KVNOuvA==",
        "code_verification_status": null,
        "default_language": "en",
        "first_billing_started_at": "2022-02-02T14:10:22Z",
        "ivr": false,
        "phone_name": "Taqnyat",
        "phone_number": "************",
        "phone_number_country": "SA",
        "verification_method": "sms",
        "was_in_use": false
      },
      "status": "ready",
      "terminated_at": null,
      "type": "whatsapp",
      "version": 1,
      "waba_account": {
        "business_capability_update": null,
        "client_id": "3eNMBBwMCL",
        "consents": {
          "signed_at": "2022-01-20T10:24:21Z"
        },
        "created_at": "2022-01-20T10:24:21Z",
        "created_by": {
          "user_id": "system",
          "user_name": "System account"
        },
        "debug_info": null,
        "external_id": "***************",
        "fb_account_status": "verified",
        "fb_business_id": "***************",
        "has_signed_consents": true,
        "id": "zl4FgyWA",
        "modified_at": "2025-05-27T07:24:36Z",
        "modified_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "name": "taqnyat Mobile information company LTD - FB ***************",
        "namespace": "89c59b35_397e_4ea9_99b7_55f187ac0e3a",
        "on_behalf_of_business_info": {
          "id": "***************",
          "name": "Taqnyat ",
          "status": "approved",
          "type": "EXISTING_CLIENT"
        },
        "partner_id": "GbM78BPA",
        "primary_funding_id": null,
        "status": "approved",
        "timezone_id": "47"
      },
      "waba_account_id": "zl4FgyWA",
      "waba_policy_violations": null
    },
    {
      "account_mode": "live",
      "auto_recharge_enabled": false,
      "auto_update_recharge_settings": false,
      "billing_started_at": null,
      "business_capability_update": null,
      "cancelled_at": null,
      "channel_origin": "integrated_onboarding_with_solution",
      "client": {
        "client_allowed_to_add_more_channels": true,
        "client_connected_channels_count": 1,
        "client_requested_channels_count": 0,
        "contact_info": {
          "email": "<EMAIL>"
        },
        "contact_user": null,
        "created_at": "2025-06-01T12:34:52Z",
        "created_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "enabled_for_chat_support": true,
        "fb_business_id": "****************",
        "id": "p3WDggVrCL",
        "max_channels": 10,
        "meta_info": null,
        "modified_at": "2025-06-01T12:34:52Z",
        "modified_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "name": "ِشركة طبيب عن بعد ذ م م",
        "organisation": null,
        "partner_payload": null,
        "status": "active",
        "suspicious": false
      },
      "client_id": "p3WDggVrCL",
      "consents": {
        "signed_at": "2025-06-01T13:23:14Z"
      },
      "consents_signed_at": "2025-06-01T13:23:14Z",
      "created_at": "2025-06-01T13:23:15Z",
      "created_by": {
        "user_id": "system",
        "user_name": "system"
      },
      "current_balance": -1.0,
      "current_limit": "TIER_0.25K",
      "current_quality_rating": "NA",
      "current_quality_update_event": "CONNECTED",
      "external_id": "645792051954595",
      "has_inbox": false,
      "id": "MPJqa7CH",
      "integration": {
        "app_id": "183325",
        "hosting_platform_type": "meta_cloud_api",
        "id": "CiflGt",
        "premium": false,
        "state": "running",
        "token": "8964ad019cf729bba8dd5397b5d88d693b54e8f2IqGgzLwL4MgB4zVCHXetOWAwmQN6XpLO"
      },
      "integration_id": "CiflGt",
      "is_allowed_messaging_with_negative_balance": true,
      "is_allowed_to_send_template_message": true,
      "is_fraud": false,
      "is_migrated": false,
      "is_oba": false,
      "last_downgrade_at": null,
      "last_upgrade_at": null,
      "meta_status": "live",
      "modified_at": "2025-06-28T20:03:22Z",
      "modified_by": {
        "user_id": "system",
        "user_name": "system"
      },
      "on_support": false,
      "partner_id": "GbM78BPA",
      "payment_connection": null,
      "profile_info": {},
      "project": {
        "api_user_email": null,
        "app_id": "183325",
        "client_id": "p3WDggVrCL",
        "created_at": "2025-06-01T13:23:14Z",
        "created_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "id": "UXiwnqPR",
        "inbox": false,
        "license_model": "cloud",
        "modified_at": "2025-06-01T13:23:14Z",
        "modified_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "name": "ِشركة طبيب عن بعد ذ م م",
        "partner_id": "GbM78BPA",
        "status": "active",
        "webhook": null
      },
      "project_id": "UXiwnqPR",
      "recharge_balance_threshold": 0.0,
      "recharge_top_up_amount": 0.0,
      "settings": {
        "bundle_id": 16069,
        "feature_type": "only_waba_sharing",
        "tier": "regular"
      },
      "setup_info": {
        "certificate": "CmgKJAik3Y7Vko2IAhIGZW50OndhIgtLaW5kYWhlYWx0aFDDqPHBBhpAIQfIqkAKNXQDUsvE5lOvvnsOJ8KPN95R3qz4pNc3l2lrcUsD6yWXVVHwoZl1QsM+xa+oJ480/m6qf0xTWp10DxIubWwt1Mre1sr2WrW7n69uLpJT7edUzdje7j1ATq08TX/DMdkhC7ldjqVozInhOA==",
        "default_language": "en",
        "ivr": false,
        "phone_name": "Kindahealth",
        "phone_number": "***********",
        "phone_number_country": "BH",
        "registration_error": null,
        "verification_method": "voice",
        "was_in_use": false
      },
      "status": "ready",
      "terminated_at": null,
      "type": "whatsapp",
      "version": 2,
      "waba_account": {
        "business_capability_update": null,
        "client_id": "p3WDggVrCL",
        "consents": {},
        "created_at": "2025-06-01T13:23:00Z",
        "created_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "debug_info": null,
        "external_id": "****************",
        "fb_account_status": "rejected",
        "fb_business_id": "****************",
        "has_signed_consents": false,
        "id": "zgSPq6WA",
        "modified_at": "2025-06-02T13:45:30Z",
        "modified_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "name": "ِشركة طبيب عن بعد ذ م م",
        "namespace": "c0bd4106_4136_4b8b_a2f7_66cc238ad117",
        "on_behalf_of_business_info": null,
        "partner_id": "GbM78BPA",
        "primary_funding_id": null,
        "status": "approved",
        "timezone_id": "76"
      },
      "waba_account_id": "zgSPq6WA",
      "waba_policy_violations": null
    },
    {
      "account_mode": "live",
      "auto_recharge_enabled": false,
      "auto_update_recharge_settings": false,
      "billing_started_at": null,
      "business_capability_update": null,
      "cancelled_at": null,
      "channel_origin": "integrated_onboarding_with_solution",
      "client": {
        "client_allowed_to_add_more_channels": true,
        "client_connected_channels_count": 1,
        "client_requested_channels_count": 0,
        "contact_info": {
          "email": "<EMAIL>"
        },
        "contact_user": {},
        "created_at": "2025-04-30T08:02:14Z",
        "created_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "enabled_for_chat_support": true,
        "fb_business_id": "****************",
        "id": "QxO3uhUCCL",
        "max_channels": 10,
        "meta_info": {},
        "modified_at": "2025-06-24T10:29:55Z",
        "modified_by": {
          "user_id": "h9TNVgU",
          "user_name": "العربية للتنمية والاستثمار"
        },
        "name": "العربية للتنمية والاستثمار",
        "organisation": null,
        "partner_payload": null,
        "status": "active",
        "suspicious": false
      },
      "client_id": "QxO3uhUCCL",
      "consents": {
        "signed_at": "2025-06-24T11:46:44Z"
      },
      "consents_signed_at": "2025-06-24T11:46:44Z",
      "created_at": "2025-06-24T11:46:44Z",
      "created_by": {
        "user_id": "system",
        "user_name": "system"
      },
      "current_balance": -1.0,
      "current_limit": "TIER_1K",
      "current_quality_rating": "High",
      "current_quality_update_event": "CONNECTED",
      "external_id": "667125293158011",
      "has_inbox": false,
      "id": "qIck47CH",
      "integration": {
        "app_id": "185550",
        "hosting_platform_type": "meta_cloud_api",
        "id": "lP1Rdo",
        "premium": false,
        "state": "running",
        "token": "19b6945bcc33ab6b1a34e89b110af5106120200dMT83otrxZmyvlspU2HpPefDCAdQ9KvpR"
      },
      "integration_id": "lP1Rdo",
      "is_allowed_messaging_with_negative_balance": true,
      "is_allowed_to_send_template_message": true,
      "is_fraud": false,
      "is_migrated": false,
      "is_oba": false,
      "last_downgrade_at": null,
      "last_upgrade_at": null,
      "meta_status": "live",
      "modified_at": "2025-06-28T20:02:55Z",
      "modified_by": {
        "user_id": "system",
        "user_name": "system"
      },
      "on_support": false,
      "partner_id": "GbM78BPA",
      "payment_connection": null,
      "profile_info": {},
      "project": {
        "api_user_email": null,
        "app_id": "185550",
        "client_id": "QxO3uhUCCL",
        "created_at": "2025-06-24T11:46:44Z",
        "created_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "id": "LahMObPR",
        "inbox": false,
        "license_model": "cloud",
        "modified_at": "2025-06-24T11:46:44Z",
        "modified_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "name": "الشركة العربية للتنمية والاستثمار",
        "partner_id": "GbM78BPA",
        "status": "active",
        "webhook": null
      },
      "project_id": "LahMObPR",
      "recharge_balance_threshold": 0.0,
      "recharge_top_up_amount": 0.0,
      "settings": {
        "bundle_id": 16069,
        "feature_type": "only_waba_sharing",
        "tier": "regular"
      },
      "setup_info": {
        "certificate": null,
        "default_language": "en",
        "ivr": false,
        "phone_name": "العربية للتنمية والاستثمار",
        "phone_number": "************",
        "phone_number_country": "SA",
        "registration_error": null,
        "verification_method": "voice",
        "was_in_use": false
      },
      "status": "ready",
      "terminated_at": null,
      "type": "whatsapp",
      "version": 2,
      "waba_account": {
        "business_capability_update": null,
        "client_id": "QxO3uhUCCL",
        "consents": {},
        "created_at": "2025-06-24T11:46:33Z",
        "created_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "debug_info": null,
        "external_id": "****************",
        "fb_account_status": "verified",
        "fb_business_id": "****************",
        "has_signed_consents": false,
        "id": "YtxfgNWA",
        "modified_at": "2025-06-24T12:12:34Z",
        "modified_by": {
          "user_id": "hQN1QmU",
          "user_name": "CS TOOL"
        },
        "name": "الشركة العربية للتنمية والاستثمار",
        "namespace": "731eedc6_550e_4630_bd06_06f7e07553bc",
        "on_behalf_of_business_info": {
          "id": "****************",
          "name": "الشركة العربية للتنمية والاستثمار",
          "status": "approved",
          "type": "SELF"
        },
        "partner_id": "GbM78BPA",
        "primary_funding_id": null,
        "status": "approved",
        "timezone_id": "76"
      },
      "waba_account_id": "YtxfgNWA",
      "waba_policy_violations": null
    },
    {
      "account_mode": "live",
      "auto_recharge_enabled": false,
      "auto_update_recharge_settings": false,
      "billing_started_at": null,
      "business_capability_update": null,
      "cancelled_at": null,
      "channel_origin": "integrated_onboarding_with_solution",
      "client": {
        "client_allowed_to_add_more_channels": true,
        "client_connected_channels_count": 1,
        "client_requested_channels_count": 0,
        "contact_info": {
          "email": "<EMAIL>"
        },
        "contact_user": null,
        "created_at": "2025-06-04T09:42:57Z",
        "created_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "enabled_for_chat_support": true,
        "fb_business_id": "****************",
        "id": "zjT1lqh2CL",
        "max_channels": 10,
        "meta_info": null,
        "modified_at": "2025-06-04T09:42:57Z",
        "modified_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "name": "شركة صناعات الاغذيه المتحده المحدوده",
        "organisation": null,
        "partner_payload": null,
        "status": "active",
        "suspicious": false
      },
      "client_id": "zjT1lqh2CL",
      "consents": {
        "signed_at": "2025-06-04T10:46:57Z"
      },
      "consents_signed_at": "2025-06-04T10:46:57Z",
      "created_at": "2025-06-04T10:46:57Z",
      "created_by": {
        "user_id": "system",
        "user_name": "system"
      },
      "current_balance": -1.0,
      "current_limit": "TIER_10K",
      "current_quality_rating": "High",
      "current_quality_update_event": "CONNECTED",
      "external_id": "677835228746104",
      "has_inbox": false,
      "id": "bdhNEBCH",
      "integration": {
        "app_id": "183640",
        "hosting_platform_type": "meta_cloud_api",
        "id": "up6tJ9",
        "premium": false,
        "state": "running",
        "token": "4c7929e4fa9530e162457de6af48b92814929d55pv9uX1iwlIKIy3veo0NOJVz5TllldznR"
      },
      "integration_id": "up6tJ9",
      "is_allowed_messaging_with_negative_balance": true,
      "is_allowed_to_send_template_message": true,
      "is_fraud": false,
      "is_migrated": false,
      "is_oba": false,
      "last_downgrade_at": null,
      "last_upgrade_at": null,
      "meta_status": "live",
      "modified_at": "2025-06-28T20:02:51Z",
      "modified_by": {
        "user_id": "system",
        "user_name": "system"
      },
      "on_support": false,
      "partner_id": "GbM78BPA",
      "payment_connection": null,
      "profile_info": {},
      "project": {
        "api_user_email": null,
        "app_id": "183640",
        "client_id": "zjT1lqh2CL",
        "created_at": "2025-06-04T10:46:57Z",
        "created_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "id": "1MDjJFPR",
        "inbox": false,
        "license_model": "cloud",
        "modified_at": "2025-06-04T10:46:57Z",
        "modified_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "name": "شركة صناعات الاغذيه المتحده المحدودة - ديمه",
        "partner_id": "GbM78BPA",
        "status": "active",
        "webhook": null
      },
      "project_id": "1MDjJFPR",
      "recharge_balance_threshold": 0.0,
      "recharge_top_up_amount": 0.0,
      "settings": {
        "bundle_id": 16069,
        "feature_type": "only_waba_sharing",
        "tier": "regular"
      },
      "setup_info": {
        "certificate": "CmMKHwi8qpCys7OcAxIGZW50OndhIgZEZWVtYWhQosiAwgYaQLDPgp0cOIbQaHoqVaYahP+DtKfFYHAWUcB8y/D+06u1sCiGHOlDNbDeJhkmT5T+JYCg8t31XvsB/+ZRjnAE9AcSL21sCo7v0IKK8Fqyu56qaCqRXuHgXMbxBeQEAYqLHPw+YsiIgfVihKABh/bPMUhU",
        "default_language": "en",
        "ivr": false,
        "phone_name": "Deemah",
        "phone_number": "************",
        "phone_number_country": "SA",
        "registration_error": null,
        "verification_method": "voice",
        "was_in_use": false
      },
      "status": "ready",
      "terminated_at": null,
      "type": "whatsapp",
      "version": 2,
      "waba_account": {
        "business_capability_update": null,
        "client_id": "zjT1lqh2CL",
        "consents": {},
        "created_at": "2025-06-04T10:46:43Z",
        "created_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "debug_info": null,
        "external_id": "****************",
        "fb_account_status": "verified",
        "fb_business_id": "****************",
        "has_signed_consents": false,
        "id": "YRI2eAWA",
        "modified_at": "2025-06-04T13:38:38Z",
        "modified_by": {
          "user_id": "system",
          "user_name": "system"
        },
        "name": "شركة صناعات الاغذيه المتحده المحدودة - ديمه",
        "namespace": "f5d310d0_76c2_4d94_943d_25ee3cd89e17",
        "on_behalf_of_business_info": null,
        "partner_id": "GbM78BPA",
        "primary_funding_id": null,
        "status": "approved",
        "timezone_id": "76"
      },
      "waba_account_id": "YRI2eAWA",
      "waba_policy_violations": null
    }
  ],
  "sort": [
    "-modified_at"
  ],
  "total": 162,
  "total_all": 162
}



