curl 'https://authx360.hub-production.360dialog.io/v1/user_profile' \
-H 'Host: authx360.hub-production.360dialog.io' \
-H 'Connection: keep-alive' \
-H 'sec-ch-ua-platform: "macOS"' \
-H 'Authorization: Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36' \
-H 'Accept: application/json, text/plain, */*' \
-H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
-H 'sec-ch-ua-mobile: ?0' \
-H 'Origin: https://hub.360dialog.com' \
-H 'Sec-Fetch-Site: cross-site' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Referer: https://hub.360dialog.com/' \
-H 'Accept-Language: en-US,en;q=0.9' \


======================================================================================================
I need to provide the bearer token to get the user profile
expected results


{
  "clients": [
    {
      "id": "49BbOTfMCL",
      "name": "اكواد العربية",
      "partner_id": "GbM78BPA"
    },
    {
      "id": "Sct539SCCL",
      "name": "Honda",
      "partner_id": "GbM78BPA"
    }
  ],
  "active_partner": "GbM78BPA",
  "organizations": [
    {
      "partner_id": "GbM78BPA",
      "org_name": "gbm78bpa"
    }
  ],
  "permissions": [
    "api_keys:manage",
    "audit_log:create",
    "bundles:read",
    "bundles:write",
    "notifications:subscribe",
    "partner:manage",
    "tos:accept",
    "tos:read",
    "user:manage"
  ],
  "roles": [
    "partner-member"
  ],
  "email": "<EMAIL>",
  "email_verified": true,
  "picture": "https://s.gravatar.com/avatar/72d89a33f52c3ce67835029e8f646af8?s=480&r=pg&d=https%3A%2F%2Fcdn.auth0.com%2Favatars%2Fan.png",
  "name": "Anas",
  "nickname": "a.younis.taqnyat.sa",
  "last_managed": {
    "channel_id": null
  }
}