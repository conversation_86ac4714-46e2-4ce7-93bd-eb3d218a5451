curl 'https://cp.taqnyat.sa/renderData.php' \
-X POST \
-H 'Host: cp.taqnyat.sa' \
-H 'Connection: keep-alive' \
-H 'Authorization: Basic YWRtaW46W0tLMmoqKioxOTExOSkpMTFdXQ==' \
-H 'sec-ch-ua-platform: "macOS"' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
-H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
-H 'sec-ch-ua-mobile: ?0' \
-H 'Accept: */*' \
-H 'Origin: https://cp.taqnyat.sa' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Referer: https://cp.taqnyat.sa/index.php' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Content-Type: application/x-www-form-urlencoded' \
--cookie '_clck=py1yak%7C2%7Cfwn%7C0%7C1953; ticketTypePay=8%2C13%2C21%2C24%2C27%2C32%2C35%2C39%2C46%2C47%2C51%2C66%2C70%2C80%2C82%2C84%2C86%2C88%2C90%2C92%2C94%2C96%2C98%2C100%2C102%2C104%2C106%2C107%2C109%2C111%2C113%2C114%2C116%2C118%2C120%2C122%2C124%2C126%2C130%2C132%2C134%2C136%2C138%2C140%2C142%2C144%2C146%2C148%2C152%2C154%2C156%2C158%2C160%2C162%2C164%2C166%2C168%2C170%2C172%2C174%2C176%2C178%2C181%2C183%2C185%2C187%2C189%2C191%2C193%2C195%2C197%2C199%2C201%2C203%2C205%2C207%2C209%2C211%2C213%2C214%2C216%2C218%2C220%2C222%2C224%2C226%2C244%2C246%2C248%2C250%2C252%2C254%2C256%2C258%2C260%2C262%2C264%2C266%2C268%2C270%2C; power=-; userNameT=a.younis; refreshPage=0; PHPSESSID=mhqp434e4ffu7a3t8vhun22d94' \
--data-raw 'TN=CRM&T=1&SL=0&EL=200&F=&O=&S=<S><S><S><S><S><S><S><S><S><S><S><S><S><S><S><S><S><S><S><S><S><S><S><S><S><S><S><S><S><S><S>CRMWA<S>' \


======================================================================================================


EL=200 this the max record i can get from the api so if i want to get the next page 

TN=CRM&T=1&SL=200&EL=200&F=&O 
which mean the offset is 200 and the limit is 200 so i will get the next 200 records



======================================================================================================

expected results 

id,#,
        محادثه ,مصدر العميل,الحساب الرئيسي ,اشتراك العميل 	  ,
        
نوع الطلب,
اسم الجهة   ,
مرحلة الصفقة,حاله الصفقة,سبب الخساره,تواصل مع العميل,تاريخ التواصل ,ملاحظه التواصل ,
الموظف,
الفرع, واتس اب <img src='images/abacus.png'>,
واتس اب <img src='images/bank.png'>,
التاريخ,
اورق العميل ,PO,Email,
ملاحظات [-##-]1674[-##-]5[-##-]77873|-##-|<font color='black'>77873</font>|-##-|<img src="images/chatS.png">&nbsp;&nbsp;<span class="" onclick=sendRequestPostSelectDataGrid("renderData.php","TN=crmChatM&id=77873");  title="محادثه المبيعات"><img src="images/chatM.png"></span>&nbsp;&nbsp;<span class="" onclick=sendRequestPostSelectDataGrid("renderData.php","TN=crmNote&id=77873");  title="ملاحظات خاصه"><img src="images/2993725.png" style="width:20px"></span>|-##-|H2025-google-sms-search|-##-|Tmp341751126462|-##-|<font color='black'>Tmp341751126462</font>|-##-|<font color='black'>فرصه</font>|-##-|شاليهات |-##-| عميل محتمل   10%|-##-||-##-||-##-|<span style=' cursor:pointer;cursor:hand;' onclick=sendRequestPostSelectDataGrid('renderData.php','TN=crmAleart&id=77873');></span>|-##-|// :|-##-||-##-|a.warasneh|-##-|Site|-##-|1|-##-|1|-##-|2025-06-28|-##-||-##-|PO|-##-|email|-##-|
There is a similar request Crm ID 27310 for employee k.a.mohammed
 Do you have a commercial registration? no|-##-||-#br#-|77861|-##-|<font color='black'>77861</font>|-##-|<img src="images/chatS.png">&nbsp;&nbsp;<span class="" onclick=sendRequestPostSelectDataGrid("renderData.php","TN=crmChatM&id=77861");  title="محادثه المبيعات"><img src="images/chatM.png"></span>&nbsp;&nbsp;<span class="" onclick=sendRequestPostSelectDataGrid("renderData.php","TN=crmNote&id=77861");  title="ملاحظات خاصه"><img src="images/2993725.png" style="width:20px"></span>|-##-|google.com|-##-|Tmp931751034927|-##-|<font color='black'>Tmp931751034927</font>|-##-|<font color='black'>فرصه</font>|-##-|tadawulcom|-##-| عميل محتمل   10%|-##-||-##-||-##-|<span style=' cursor:pointer;cursor:hand;' onclick=sendRequestPostSelectDataGrid('renderData.php','TN=crmAleart&id=77861');></span>|-##-|// :|-##-||-##-|a.warasneh|-##-|Site|-##-|1|-##-|1|-##-|2025-06-27|-##-||-##-|PO|-##-|email|-##-|
There is a similar request Crm ID 712 for employee lostClients
 Do you have a commercial registration? yes|-##-||-#br#-|77841|-##-|<font color='black'>77841</font>|-##-|<img src="images/chatS.png">&nbsp;&nbsp;<span class="" onclick=sendRequestPostSelectDataGrid("renderData.php","TN=crmChatM&id=77841");  title="محادثه المبيعات"><img src="images/chatM.png"></span>&nbsp;&nbsp;<span class="" onclick=sendRequestPostSelectDataGrid("renderData.php","TN=crmNote&id=77841");  title="ملاحظات خاصه"><img src="images/2993725.png" style="width:20px"></span>|-##-|H2025-google-sms-search|-##-|Tmp111750944080|-##-|<font color='black'>Tmp111750944080</font>|-##-|<font color='black'>فرصه</font>|-##-|مركز قدرة التداوي الطبية|-##-| عميل محتمل   10%|-##-||-##-||-##-|<span style=' cursor:pointer;cursor:hand;' onclick=sendRequestPostSelectDataGrid('renderData.php','TN=crmAleart&id=77841');></span>|-##-|// :|-##-||-##-|i.said.t|-##-|telesales|-##-|1|-##-|1|-##-|2025-06-26|-##-||-##-|PO|-##-|email|-##-|

 Do you have a commercial registration? yes|-##-||-#br#-|77836|-##-|<span title='تم تنفيد الطلب من قبل الدعم الفني'><font color='green'>77836</font></span>|-##-|<span class="blink" onclick=sendRequestPostSelectDataGrid("renderData.php","TN=crmChat&id=77836");  title="محادثه الدعم الفني"><img src="images/chatS.png"></span>&nbsp;&nbsp;<span class="" onclick=sendRequestPostSelectDataGrid("renderData.php","TN=crmChatM&id=77836");  title="محادثه المبيعات"><img src="images/chatM.png"></span>&nbsp;&nbsp;<span class="" onclick=sendRequestPostSelectDataGrid("renderData.php","TN=crmNote&id=77836");  title="ملاحظات خاصه"><img src="images/2993725.png" style="width:20px"></span>|-##-|products|-##-|egyac|-##-|<span title='حساب مفعل'><font color='green'>egyac</font></span><span title='نقص باوراق اعتماد العميل '><img  class='blink' src='images/uncompleted.png'></span>|-##-|<font color='black'>فرصه</font>|-##-|مكتب السداد الآلي للخدمات العامة|-##-|عملاء مؤهلون      35%|-##-||-##-||-##-|<span style=' cursor:pointer;cursor:hand;' onclick=sendRequestPostSelectDataGrid('renderData.php','TN=crmAleart&id=77836');>Had a phone conversation</span>|-##-|2025/06/26 14:12|-##-||-##-|y.alamoudi|-##-|products|-##-|1|-##-|1|-##-|2025-06-26|-##-|غير مكتمله   |-##-|PO|-##-|email|-##-|taxNumber:0|-##-||-#br#-|77817|-##-|<font color='black'>77817</font>|-##-|<img src="images/chatS.png">&nbsp;&nbsp;<span class="" onclick=sendRequestPostSelectDataGrid("renderData.php","TN=crmChatM&id=77817");  title="محادثه المبيعات"><img src="images/chatM.png"></span>&nbsp;&nbsp;<span class="" onclick=sendRequestPostSelectDataGrid("renderData.php","TN=crmNote&id=77817");  title="ملاحظات خاصه"><img src="images/2993725.png" style="width:20px"></span>|-##-|google.com|-##-|Tmp691750929385|-##-|<font color='black'>Tmp691750929385</font>|-##-|<font color='black'>فرصه</font>|-##-|جمعية للخدمات الاجتماعية|-##-| عميل محتمل   10%|-##-||-##-||-##-|<span style=' cursor:pointer;cursor:hand;' onclick=sendRequestPostSelectDataGrid('renderData.php','TN=crmAleart&id=77817');></span>|-##-|2025/06/26 13:45|-##-||-##-|a.alkhudairi|-##-|Site|-##-|1|-##-|1|-##-|2025-06-26|-##-||-##-|PO|-##-|email|-##-|There is a similar request Crm ID 75975 for employee m.ali
 Do you have a commercial registration? yes
a.warasneh move the deal from a.warasneh to a.alkhudairi
|-##-||-#br#-|