curl 'https://cp.taqnyat.sa/renderData.php' \
-X POST \
-H 'Host: cp.taqnyat.sa' \
-H 'Connection: keep-alive' \
-H 'Authorization: Basic YWRtaW46W0tLMmoqKioxOTExOSkpMTFdXQ==' \
-H 'sec-ch-ua-platform: "macOS"' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
-H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
-H 'sec-ch-ua-mobile: ?0' \
-H 'Accept: */*' \
-H 'Origin: https://cp.taqnyat.sa' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Referer: https://cp.taqnyat.sa/index.php' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Content-Type: application/x-www-form-urlencoded' \
--cookie '_clck=py1yak%7C2%7Cfwn%7C0%7C1953; ticketTypePay=8%2C13%2C21%2C24%2C27%2C32%2C35%2C39%2C46%2C47%2C51%2C66%2C70%2C80%2C82%2C84%2C86%2C88%2C90%2C92%2C94%2C96%2C98%2C100%2C102%2C104%2C106%2C107%2C109%2C111%2C113%2C114%2C116%2C118%2C120%2C122%2C124%2C126%2C130%2C132%2C134%2C136%2C138%2C140%2C142%2C144%2C146%2C148%2C152%2C154%2C156%2C158%2C160%2C162%2C164%2C166%2C168%2C170%2C172%2C174%2C176%2C178%2C181%2C183%2C185%2C187%2C189%2C191%2C193%2C195%2C197%2C199%2C201%2C203%2C205%2C207%2C209%2C211%2C213%2C214%2C216%2C218%2C220%2C222%2C224%2C226%2C244%2C246%2C248%2C250%2C252%2C254%2C256%2C258%2C260%2C262%2C264%2C266%2C268%2C270%2C; power=-; userNameT=a.younis; refreshPage=0; PHPSESSID=mhqp434e4ffu7a3t8vhun22d94' \
--data-raw 'TN=waUser&SL=0&EL=15&S=<SR><SR><SR>&CID=' \

======================================================================================================

to get the full data i need to use offset 

expected results 

id,id,phoneNumber,mobile,dateStart,expiryDate,margin,discount,active,invoiceType,invoiceId,invoice,monthlyFees,'prefix',servicePackage,'serviceConsumption',utilityPackage,'utilityConsumption',marketingPackage,'marketingConsumption',authenticationPackage,'authenticationConsumption',monthlyFeesChat,liveChat,liveChatSeat,monthlyFeesChatBot,chatBot,botMsgPrice,initiatedBotDiscount,'botConsumption',mailAccount,mailPackage,botId,accessToken[-##-]5116[-##-]15[-##-]1270072|-##-|1270072|-##-|************|-##-|luqt1|-##-|********|-##-|********|-##-|0.06000|-##-|0|-##-|1|-##-|1|-##-||-##-|1|-##-|0|-##-|966<br>|-##-|0<br>|-##-|24<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|3505<br>|-##-|0<br>|-##-|0<br>|-##-|62|-##-|1|-##-|2|-##-|0|-##-|1|-##-|0.0000|-##-|0|-##-|8205|-##-|0|-##-|0|-##-||-##-|AAMWF|-##-||-#br#-|1252143|-##-|1252143|-##-|************|-##-|national-sch|-##-|********|-##-|********|-##-|0.06000|-##-|0|-##-|1|-##-|1|-##-||-##-|1|-##-|500|-##-|966<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|31|-##-|1|-##-|1|-##-|130|-##-|1|-##-|0.1400|-##-|1000|-##-|0|-##-|0|-##-|0|-##-||-##-|AAMWF|-##-||-#br#-|1266584|-##-|1266584|-##-|************|-##-|alsalmanoptics|-##-|********|-##-|********|-##-|0.06000|-##-|0|-##-|1|-##-|1|-##-||-##-|1|-##-|0|-##-|966<br>|-##-|0<br>|-##-|35<br>|-##-|0<br>|-##-|1<br>|-##-|0<br>|-##-|1<br>|-##-|0<br>|-##-|0<br>|-##-|0|-##-|1|-##-|4|-##-|0|-##-|0|-##-|0.0000|-##-|0|-##-|16|-##-|0|-##-|0|-##-||-##-|AAMWF|-##-||-#br#-|1213322|-##-|1213322|-##-|966920008601|-##-|alfia|-##-||-##-|20251231|-##-|0.01875|-##-|0|-##-|1|-##-|1|-##-||-##-|0|-##-|4763|-##-|966<br>|-##-|0<br>|-##-|24<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0|-##-|1|-##-|5|-##-|0|-##-|1|-##-|0.0000|-##-|0|-##-|58|-##-|0|-##-|0|-##-||-##-|AAMWF|-##-||-#br#-|1252913|-##-|1252913|-##-||-##-|canarysa|-##-||-##-|20281231|-##-|0.01875|-##-|0|-##-|1|-##-|1|-##-||-##-|1|-##-|4763|-##-|966<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0|-##-|1|-##-|1|-##-|0|-##-|1|-##-|0.0000|-##-|0|-##-|10|-##-|0|-##-|0|-##-||-##-|AAMWF|-##-||-#br#-|1270602|-##-|1270602|-##-|966920035413|-##-|riyadhrehab|-##-|20250618|-##-|********|-##-|0.06000|-##-|0|-##-|1|-##-|1|-##-||-##-|1|-##-|0|-##-|966<br>|-##-|0<br>|-##-|5<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|1196<br>|-##-|0<br>|-##-|0<br>|-##-|0|-##-|1|-##-|1|-##-|0|-##-|1|-##-|0.1400|-##-|1000|-##-|1041|-##-|0|-##-|0|-##-||-##-|AAMWF|-##-||-#br#-|1260698|-##-|1260698|-##-|966920034090|-##-|cactus|-##-|20250424|-##-|20260501|-##-|0.06000|-##-|0|-##-|1|-##-|3|-##-||-##-|0|-##-|0|-##-|966<br>|-##-|0<br>|-##-|0<br>|-##-|5000<br>|-##-|3<br>|-##-|10000<br>|-##-|1<br>|-##-|0<br>|-##-|0<br>|-##-|0|-##-|1|-##-|1|-##-|0|-##-|1|-##-|0.1400|-##-|10000|-##-|1|-##-|0|-##-|0|-##-||-##-|AAMWF|-##-||-#br#-|1267756|-##-|1267756|-##-|966500081439|-##-|Linat|-##-|20250322|-##-|20260401|-##-|0.06000|-##-|0|-##-|1|-##-|1|-##-||-##-|1|-##-|0|-##-|966<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0|-##-|1|-##-|1|-##-|0|-##-|0|-##-|0.0000|-##-|0|-##-|0|-##-|0|-##-|0|-##-||-##-|AAMWF|-##-||-#br#-|1271981|-##-|1271981|-##-||-##-|nadr1|-##-||-##-|20200101|-##-|0.06000|-##-|0|-##-|1|-##-|1|-##-||-##-|0|-##-|0|-##-|966<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0|-##-|1|-##-|1|-##-|130|-##-|1|-##-|0.1400|-##-|1000|-##-|0|-##-|0|-##-|0|-##-|Y4FIh|-##-||-##-||-#br#-|1271973|-##-|1271973|-##-||-##-|abd2025|-##-||-##-|20250928|-##-|0.06000|-##-|0|-##-|1|-##-|1|-##-||-##-|0|-##-|0|-##-|966<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0|-##-|1|-##-|1|-##-|130|-##-|1|-##-|0.1400|-##-|1000|-##-|0|-##-|0|-##-|0|-##-|Y4FIh|-##-||-##-||-#br#-|1271963|-##-|1271963|-##-||-##-|707ahmed|-##-||-##-|20250928|-##-|0.06000|-##-|0|-##-|1|-##-|1|-##-||-##-|0|-##-|0|-##-|966<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0|-##-|1|-##-|1|-##-|130|-##-|1|-##-|0.1400|-##-|1000|-##-|0|-##-|0|-##-|0|-##-|Y4FIh|-##-||-##-||-#br#-|1271961|-##-|1271961|-##-||-##-|esnnad|-##-||-##-|20200101|-##-|0.06000|-##-|0|-##-|1|-##-|1|-##-||-##-|0|-##-|0|-##-|966<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0|-##-|1|-##-|1|-##-|130|-##-|1|-##-|0.1400|-##-|1000|-##-|0|-##-|0|-##-|0|-##-|Y4FIh|-##-||-##-||-#br#-|1271955|-##-|1271955|-##-||-##-|velvetclinics|-##-||-##-|20200101|-##-|0.06000|-##-|0|-##-|1|-##-|1|-##-||-##-|0|-##-|0|-##-|966<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0|-##-|1|-##-|1|-##-|130|-##-|1|-##-|0.1400|-##-|1000|-##-|0|-##-|0|-##-|0|-##-|Y4FIh|-##-||-##-||-#br#-|1271947|-##-|1271947|-##-||-##-|amanamn|-##-||-##-|20200101|-##-|0.06000|-##-|0|-##-|1|-##-|1|-##-||-##-|0|-##-|0|-##-|966<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0|-##-|1|-##-|1|-##-|130|-##-|1|-##-|0.1400|-##-|1000|-##-|0|-##-|0|-##-|0|-##-|Y4FIh|-##-||-##-||-#br#-|1271945|-##-|1271945|-##-||-##-|hamadali|-##-||-##-|20200101|-##-|0.06000|-##-|0|-##-|1|-##-|1|-##-||-##-|0|-##-|0|-##-|966<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0<br>|-##-|0|-##-|1|-##-|1|-##-|130|-##-|1|-##-|0.1400|-##-|1000|-##-|0|-##-|0|-##-|0|-##-|Y4FIh|-##-||-##-||-#br#-|


