<?php

namespace App\Http\Controllers;

use App\Services\Api\Dialog360Service;
use App\Http\Requests\Dialog360TokenRequest;
use App\Exceptions\ApiIntegrationException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class Dialog360SettingsController extends Controller
{
    private $dialog360Service;

    public function __construct(Dialog360Service $dialog360Service)
    {
        $this->dialog360Service = $dialog360Service;
    }

    /**
     * Show 360Dialog settings page
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $authStatus = $this->dialog360Service->getAuthStatus();
        
        return view('settings.360dialog', [
            'authStatus' => $authStatus
        ]);
    }

    /**
     * Update Bearer token
     *
     * @param Dialog360TokenRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateToken(Dialog360TokenRequest $request)
    {
        try {
            $bearerToken = $request->input('bearer_token');

            Log::info('360Dialog token update initiated', [
                'user_id' => auth()->id(),
                'token_length' => strlen($bearerToken)
            ]);

            $success = $this->dialog360Service->setBearerToken($bearerToken);

            if ($success) {
                Log::info('360Dialog token updated successfully', [
                    'user_id' => auth()->id()
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Bearer token updated and validated successfully'
                ]);
            } else {
                Log::warning('360Dialog token validation failed', [
                    'user_id' => auth()->id(),
                    'token_length' => strlen($bearerToken)
                ]);

                throw ApiIntegrationException::invalidToken('360dialog', 'Invalid Bearer token. Please check your token and try again.');
            }
        } catch (ApiIntegrationException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('360Dialog token update exception', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            throw ApiIntegrationException::serviceUnavailable('360dialog', 'Token validation service temporarily unavailable. Please try again.');
        }
    }

    /**
     * Test Bearer token connection
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function testConnection()
    {
        $authStatus = $this->dialog360Service->getAuthStatus();

        if (!$authStatus['authenticated']) {
            return response()->json([
                'success' => false,
                'message' => $authStatus['message']
            ], 401);
        }

        // Test with account info endpoint
        $result = $this->dialog360Service->getAccountInfo();

        return response()->json([
            'success' => $result['success'],
            'message' => $result['success'] 
                ? 'Connection test successful' 
                : 'Connection test failed: ' . ($result['message'] ?? 'Unknown error'),
            'data' => $result['data'] ?? null,
            'error' => $result['error'] ?? null
        ]);
    }

    /**
     * Get authentication status
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAuthStatus()
    {
        $authStatus = $this->dialog360Service->getAuthStatus();

        return response()->json($authStatus);
    }

    /**
     * Disconnect from 360Dialog
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function disconnect()
    {
        $this->dialog360Service->disconnect();

        return response()->json([
            'success' => true,
            'message' => 'Successfully disconnected from 360Dialog'
        ]);
    }

    /**
     * Get account information
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAccountInfo()
    {
        $result = $this->dialog360Service->getAccountInfo();

        return response()->json($result);
    }

    /**
     * Get message templates
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTemplates()
    {
        $result = $this->dialog360Service->getTemplates();

        return response()->json($result);
    }

    /**
     * Sync templates from 360Dialog
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function syncTemplates()
    {
        $result = $this->dialog360Service->getTemplates();

        if ($result['success']) {
            // Here you would typically store the templates in your database
            // For now, we'll just return the success response
            
            return response()->json([
                'success' => true,
                'message' => 'Templates synced successfully',
                'templates_count' => count($result['data']['templates'] ?? [])
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to sync templates: ' . ($result['message'] ?? 'Unknown error')
        ], 400);
    }

    /**
     * Sync traffic data from 360Dialog
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function syncTraffic()
    {
        $result = $this->dialog360Service->syncTrafficData();

        return response()->json($result);
    }

    /**
     * Send test message
     *
     * @param Dialog360TokenRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendTestMessage(Dialog360TokenRequest $request)
    {
        try {
            $to = $request->input('to');
            $message = $request->input('message');

            Log::info('360Dialog test message initiated', [
                'user_id' => auth()->id(),
                'to' => $to
            ]);

            $result = $this->dialog360Service->sendMessage($to, $message);

            if ($result['success']) {
                Log::info('360Dialog test message sent successfully', [
                    'user_id' => auth()->id(),
                    'to' => $to
                ]);
            } else {
                Log::warning('360Dialog test message failed', [
                    'user_id' => auth()->id(),
                    'to' => $to,
                    'error' => $result['error'] ?? 'Unknown error'
                ]);
            }

            return response()->json($result);
        } catch (\Exception $e) {
            Log::error('360Dialog test message exception', [
                'user_id' => auth()->id(),
                'to' => $request->input('to'),
                'error' => $e->getMessage()
            ]);

            throw ApiIntegrationException::serviceUnavailable('360dialog', 'Message sending service temporarily unavailable. Please try again.');
        }
    }

    /**
     * Get user profile information
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserProfile()
    {
        try {
            Log::info('360Dialog user profile request', [
                'user_id' => auth()->id()
            ]);

            $result = $this->dialog360Service->getUserProfile();

            Log::info('360Dialog user profile response', [
                'user_id' => auth()->id(),
                'success' => $result['success']
            ]);

            return response()->json($result);
        } catch (\Exception $e) {
            Log::error('360Dialog user profile exception', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            throw ApiIntegrationException::serviceUnavailable('360dialog', 'User profile service temporarily unavailable. Please try again.');
        }
    }

    /**
     * Get account details (channels)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAccountDetails(Request $request)
    {
        try {
            $partnerId = $request->input('partner_id', 'GbM78BPA');
            $offset = $request->input('offset', 0);
            $limit = $request->input('limit', 50);

            Log::info('360Dialog account details request', [
                'user_id' => auth()->id(),
                'partner_id' => $partnerId,
                'offset' => $offset,
                'limit' => $limit
            ]);

            $result = $this->dialog360Service->getAccountDetails($partnerId, $offset, $limit);

            Log::info('360Dialog account details response', [
                'user_id' => auth()->id(),
                'success' => $result['success']
            ]);

            return response()->json($result);
        } catch (\Exception $e) {
            Log::error('360Dialog account details exception', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            throw ApiIntegrationException::serviceUnavailable('360dialog', 'Account details service temporarily unavailable. Please try again.');
        }
    }

    /**
     * Get traffic data for a specific channel
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTrafficData(Request $request)
    {
        try {
            $partnerId = $request->input('partner_id', 'GbM78BPA');
            $clientId = $request->input('client_id', '3eNMBBwMCL');
            $projectId = $request->input('project_id', '9tJH3tPR');
            $channelId = $request->input('channel_id', 'fCFWoZCH');

            Log::info('360Dialog traffic data request', [
                'user_id' => auth()->id(),
                'partner_id' => $partnerId,
                'client_id' => $clientId,
                'project_id' => $projectId,
                'channel_id' => $channelId
            ]);

            $result = $this->dialog360Service->getTrafficData($partnerId, $clientId, $projectId, $channelId);

            Log::info('360Dialog traffic data response', [
                'user_id' => auth()->id(),
                'success' => $result['success']
            ]);

            return response()->json($result);
        } catch (\Exception $e) {
            Log::error('360Dialog traffic data exception', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            throw ApiIntegrationException::serviceUnavailable('360dialog', 'Traffic data service temporarily unavailable. Please try again.');
        }
    }

}
