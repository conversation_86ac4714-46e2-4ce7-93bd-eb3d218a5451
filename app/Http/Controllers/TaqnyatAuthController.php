<?php

namespace App\Http\Controllers;

use App\Services\Api\TaqnyatService;
use App\Http\Requests\TaqnyatOtpRequest;
use App\Exceptions\ApiIntegrationException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TaqnyatAuthController extends Controller
{
    private $taqnyatService;

    public function __construct(TaqnyatService $taqnyatService)
    {
        $this->taqnyatService = $taqnyatService;
    }

    /**
     * Request OTP for Taqnyat authentication using authenticated user's phone number
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function requestOtp(Request $request)
    {
        try {
            $user = auth()->user();
            
            // Check if user has a phone number
            if (empty($user->phone)) {
                throw ApiIntegrationException::validationFailed('taqnyat', 'No phone number associated with your account. Please update your profile with a valid phone number.');
            }
            
            $phoneNumber = $user->phone;
            
            Log::info('Taqnyat OTP request initiated', [
                'phone_number' => $phoneNumber,
                'user_id' => $user->id,
                'user_email' => $user->email
            ]);
            
            $result = $this->taqnyatService->requestOtp($phoneNumber);

            if ($result['success']) {
                Log::info('Taqnyat OTP request successful', [
                    'phone_number' => $phoneNumber,
                    'session_id' => $result['session_id'],
                    'user_id' => $user->id
                ]);
                
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'session_id' => $result['session_id'],
                    'expires_in' => $result['expires_in'],
                    'phone_number' => $phoneNumber,
                    'demo_otp' => $result['demo_otp'] ?? null // Only for demo
                ]);
            } else {
                Log::warning('Taqnyat OTP request failed', [
                    'phone_number' => $phoneNumber,
                    'user_id' => $user->id,
                    'error' => $result['error']
                ]);
                
                throw ApiIntegrationException::requestFailed('taqnyat', $result['message']);
            }
        } catch (ApiIntegrationException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Taqnyat OTP request exception', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);
            
            throw ApiIntegrationException::serviceUnavailable('taqnyat', 'Service temporarily unavailable. Please try again.');
        }
    }

    /**
     * Verify OTP for Taqnyat authentication
     *
     * @param TaqnyatOtpRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyOtp(TaqnyatOtpRequest $request)
    {
        try {
            $sessionId = $request->input('session_id');
            $otp = $request->input('otp');
            $user = auth()->user();

            Log::info('Taqnyat OTP verification initiated', [
                'session_id' => $sessionId,
                'user_id' => $user->id
            ]);

            $result = $this->taqnyatService->verifyOtp($sessionId, $otp);

            if ($result['success']) {
                Log::info('Taqnyat OTP verification successful', [
                    'session_id' => $sessionId,
                    'phone_number' => $result['phone_number'],
                    'user_id' => $user->id
                ]);
                
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'access_token' => $result['access_token'],
                    'phone_number' => $result['phone_number']
                ]);
            } else {
                Log::warning('Taqnyat OTP verification failed', [
                    'session_id' => $sessionId,
                    'user_id' => $user->id,
                    'error' => $result['error']
                ]);
                
                // Map error types to appropriate HTTP status codes and exceptions
                switch ($result['error']) {
                    case 'SESSION_EXPIRED':
                    case 'OTP_EXPIRED':
                        throw new ApiIntegrationException('taqnyat', $result['message'], $result['error'], 410);
                    case 'MAX_ATTEMPTS_EXCEEDED':
                        throw new ApiIntegrationException('taqnyat', $result['message'], $result['error'], 429);
                    case 'INVALID_OTP':
                        throw new ApiIntegrationException('taqnyat', $result['message'], $result['error'], 401);
                    default:
                        throw ApiIntegrationException::requestFailed('taqnyat', $result['message']);
                }
            }
        } catch (ApiIntegrationException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Taqnyat OTP verification exception', [
                'session_id' => $request->input('session_id'),
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);
            
            throw ApiIntegrationException::serviceUnavailable('taqnyat', 'Verification service temporarily unavailable. Please try again.');
        }
    }

    /**
     * Get authentication status
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAuthStatus()
    {
        $user = auth()->user();
        $isAuthenticated = $this->taqnyatService->isAuthenticated();
        $authData = $this->taqnyatService->getAuthData();

        return response()->json([
            'authenticated' => $isAuthenticated,
            'phone_number' => $authData['phone_number'] ?? $user->phone ?? null,
            'user_phone' => $user->phone ?? null,
            'has_user_phone' => !empty($user->phone),
            'authenticated_at' => $authData['authenticated_at'] ?? null,
            'expires_at' => $authData['expires_at'] ?? null
        ]);
    }

    /**
     * Logout from Taqnyat
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout()
    {
        $user = auth()->user();
        
        Log::info('Taqnyat logout initiated', [
            'user_id' => $user->id
        ]);
        
        $this->taqnyatService->logout();

        return response()->json([
            'success' => true,
            'message' => 'Successfully logged out from Taqnyat'
        ]);
    }

    /**
     * Test API connection (requires authentication)
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function testConnection()
    {
        if (!$this->taqnyatService->isAuthenticated()) {
            return response()->json([
                'success' => false,
                'message' => 'Not authenticated with Taqnyat'
            ], 401);
        }

        // Test with a simple API call
        $result = $this->taqnyatService->makeAuthenticatedRequest('/test');

        return response()->json([
            'success' => $result['success'],
            'message' => $result['success'] ? 'Connection test successful' : 'Connection test failed',
            'data' => $result['data'] ?? null,
            'error' => $result['error'] ?? null
        ]);
    }
}
