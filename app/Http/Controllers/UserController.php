<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class UserController extends Controller
{
    /**
     * Update user's phone number
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePhone(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => [
                'required',
                'string',
                'regex:/^\+?[1-9]\d{1,14}$/', // E.164 format
                'min:10',
                'max:15',
                'unique:users,phone,' . auth()->id()
            ]
        ], [
            'phone.required' => 'Phone number is required',
            'phone.regex' => 'Please enter a valid phone number in international format (e.g., +************)',
            'phone.min' => 'Phone number must be at least 10 digits',
            'phone.max' => 'Phone number must not exceed 15 digits',
            'phone.unique' => 'This phone number is already associated with another account'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();
            $phoneNumber = $request->input('phone');
            
            // Clean phone number (remove spaces, dashes, etc.)
            $phoneNumber = preg_replace('/[^\d+]/', '', $phoneNumber);
            
            // Ensure phone number starts with +
            if (!str_starts_with($phoneNumber, '+')) {
                // If it's a Saudi number starting with 5, add +966
                if (str_starts_with($phoneNumber, '5') && strlen($phoneNumber) === 9) {
                    $phoneNumber = '+966' . $phoneNumber;
                } elseif (str_starts_with($phoneNumber, '966')) {
                    $phoneNumber = '+' . $phoneNumber;
                } else {
                    $phoneNumber = '+' . $phoneNumber;
                }
            }
            
            $user->update(['phone' => $phoneNumber]);
            
            return response()->json([
                'success' => true,
                'message' => 'Phone number updated successfully',
                'phone' => $phoneNumber
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update phone number. Please try again.'
            ], 500);
        }
    }

    /**
     * Get user profile information
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProfile()
    {
        $user = auth()->user();
        
        return response()->json([
            'success' => true,
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                'created_at' => $user->created_at,
                'updated_at' => $user->updated_at
            ]
        ]);
    }
}
