<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

/**
 * Dialog360TokenRequest - Validation for 360Dialog JWT Bearer tokens
 *
 * This request class validates JWT Bearer tokens for 360Dialog WhatsApp Business API integration.
 *
 * Expected JWT Token Format:
 * - Algorithm: RS256
 * - Issuer: https://360dialog.eu.auth0.com/
 * - Audience: Contains 360dialog.io services
 * - Organization: taqnyat (for production)
 * - Environment: production
 *
 * Example Production Token:
 * ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
 */

class Dialog360TokenRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $action = $this->route()->getActionMethod();
        
        switch ($action) {
            case 'updateToken':
                return [
                    'bearer_token' => [
                        'required',
                        'string',
                        'min:1000', // 360Dialog JWT tokens are typically 1328+ characters
                        'max:1500', // Allow some buffer for 360Dialog JWT tokens
                        'regex:/^[A-Za-z0-9\-_\.=]+$/' // JWT tokens use base64url encoding (includes =)
                    ]
                ];
                
            case 'sendTestMessage':
                return [
                    'to' => [
                        'required',
                        'string',
                        'regex:/^\+?[1-9]\d{1,14}$/' // E.164 format
                    ],
                    'message' => [
                        'required',
                        'string',
                        'max:1000',
                        'min:1'
                    ]
                ];
                
            default:
                return [];
        }
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'bearer_token.required' => 'Bearer token is required',
            'bearer_token.min' => 'Bearer token must be at least 1000 characters long (360Dialog JWT tokens are typically 1328 characters)',
            'bearer_token.max' => 'Bearer token must not exceed 1500 characters',
            'bearer_token.regex' => 'Bearer token contains invalid characters. JWT tokens should only contain base64url characters (A-Z, a-z, 0-9, -, _, ., =)',
            
            'to.required' => 'Recipient phone number is required',
            'to.regex' => 'Please enter a valid phone number in international format (e.g., +966501234567)',
            
            'message.required' => 'Message content is required',
            'message.max' => 'Message must not exceed 1000 characters',
            'message.min' => 'Message cannot be empty',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'bearer_token' => 'Bearer token',
            'to' => 'phone number',
            'message' => 'message content',
        ];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'error' => 'VALIDATION_FAILED'
            ], 422)
        );
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean Bearer token if provided
        if ($this->has('bearer_token')) {
            $token = trim($this->input('bearer_token'));
            
            $this->merge([
                'bearer_token' => $token
            ]);
        }

        // Clean phone number if provided
        if ($this->has('to')) {
            $phoneNumber = $this->input('to');
            // Remove spaces, dashes, parentheses, etc.
            $cleanedPhone = preg_replace('/[^\d+]/', '', $phoneNumber);
            
            $this->merge([
                'to' => $cleanedPhone
            ]);
        }

        // Trim message content if provided
        if ($this->has('message')) {
            $message = trim($this->input('message'));
            
            $this->merge([
                'message' => $message
            ]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator(Validator $validator): void
    {
        $validator->after(function (Validator $validator) {
            // Additional custom validation logic
            
            // For phone number validation, ensure it starts with + if it doesn't already
            if ($this->has('to')) {
                $phoneNumber = $this->input('to');
                if (!empty($phoneNumber) && !str_starts_with($phoneNumber, '+')) {
                    // If it's a Saudi number starting with 5, add +966
                    if (str_starts_with($phoneNumber, '5') && strlen($phoneNumber) === 9) {
                        $this->merge(['to' => '+966' . $phoneNumber]);
                    } elseif (str_starts_with($phoneNumber, '966')) {
                        $this->merge(['to' => '+' . $phoneNumber]);
                    }
                }
            }

            // Validate JWT Bearer token format
            if ($this->has('bearer_token')) {
                $token = $this->input('bearer_token');

                // Check for common token prefixes that shouldn't be included
                $invalidPrefixes = ['Bearer ', 'bearer ', 'Token ', 'token '];
                foreach ($invalidPrefixes as $prefix) {
                    if (str_starts_with($token, $prefix)) {
                        $validator->errors()->add('bearer_token', 'Bearer token should not include the "Bearer" or "Token" prefix.');
                        break;
                    }
                }

                // Validate JWT structure (3 parts separated by 2 dots)
                $parts = explode('.', $token);
                if (count($parts) !== 3) {
                    $validator->errors()->add('bearer_token', 'Invalid JWT token format. JWT tokens must have exactly 3 parts separated by dots (header.payload.signature).');
                } else {
                    // Validate each part is valid base64url
                    foreach ($parts as $index => $part) {
                        if (empty($part)) {
                            $partNames = ['header', 'payload', 'signature'];
                            $validator->errors()->add('bearer_token', "JWT token {$partNames[$index]} part cannot be empty.");
                            break;
                        }

                        // Check if part contains only valid base64url characters
                        if (!preg_match('/^[A-Za-z0-9\-_=]*$/', $part)) {
                            $partNames = ['header', 'payload', 'signature'];
                            $validator->errors()->add('bearer_token', "JWT token {$partNames[$index]} part contains invalid characters. Only base64url characters are allowed.");
                            break;
                        }
                    }
                }
            }
        });
    }
}
