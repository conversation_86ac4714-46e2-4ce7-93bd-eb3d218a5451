<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

class TaqnyatOtpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $action = $this->route()->getActionMethod();
        
        switch ($action) {
            case 'requestOtp':
                return [
                    'phone_number' => [
                        'required',
                        'string',
                        'regex:/^\+?[1-9]\d{1,14}$/', // E.164 format
                        'min:10',
                        'max:15'
                    ]
                ];
                
            case 'verifyOtp':
                return [
                    'session_id' => [
                        'required',
                        'string',
                        'regex:/^taqnyat_otp_[a-f0-9]+$/' // Validate session ID format
                    ],
                    'otp' => [
                        'required',
                        'string',
                        'regex:/^\d{6}$/', // 6-digit numeric OTP
                        'size:6'
                    ]
                ];
                
            default:
                return [];
        }
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'phone_number.required' => 'Phone number is required',
            'phone_number.regex' => 'Please enter a valid phone number in international format (e.g., +966501234567)',
            'phone_number.min' => 'Phone number must be at least 10 digits',
            'phone_number.max' => 'Phone number must not exceed 15 digits',
            
            'session_id.required' => 'Session ID is required',
            'session_id.regex' => 'Invalid session ID format',
            
            'otp.required' => 'OTP is required',
            'otp.regex' => 'OTP must be exactly 6 digits',
            'otp.size' => 'OTP must be exactly 6 digits',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'phone_number' => 'phone number',
            'session_id' => 'session ID',
            'otp' => 'OTP code',
        ];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(Validator $validator)
    {
        $action = $this->route()->getActionMethod();
        $errors = $validator->errors();

        // Create detailed error information
        $detailedErrors = [];
        foreach ($errors->all() as $error) {
            $detailedErrors[] = $error;
        }

        // Create context-specific error messages
        $contextMessage = match($action) {
            'requestOtp' => 'OTP request validation failed. Please check your phone number format.',
            'verifyOtp' => 'OTP verification validation failed. Please check your OTP code and session.',
            default => 'Request validation failed.'
        };

        // Add debugging information for development
        $debugInfo = [];
        if (app()->environment(['local', 'testing'])) {
            $debugInfo = [
                'action' => $action,
                'input_data' => $this->except(['password', 'password_confirmation']),
                'validation_rules' => $this->rules(),
                'failed_rules' => $this->getFailedRules($validator)
            ];
        }

        throw new HttpResponseException(
            response()->json([
                'success' => false,
                'message' => $contextMessage,
                'errors' => $errors,
                'error' => 'VALIDATION_FAILED',
                'detailed_errors' => $detailedErrors,
                'context' => $action,
                'debug' => $debugInfo
            ], 422)
        );
    }

    /**
     * Get failed validation rules for debugging
     */
    private function getFailedRules(Validator $validator): array
    {
        $failedRules = [];
        foreach ($validator->failed() as $field => $rules) {
            $failedRules[$field] = array_keys($rules);
        }
        return $failedRules;
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean phone number if provided
        if ($this->has('phone_number')) {
            $phoneNumber = $this->input('phone_number');
            // Remove spaces, dashes, parentheses, etc.
            $cleanedPhone = preg_replace('/[^\d+]/', '', $phoneNumber);
            
            $this->merge([
                'phone_number' => $cleanedPhone
            ]);
        }

        // Clean OTP if provided
        if ($this->has('otp')) {
            $otp = $this->input('otp');
            // Remove any non-digit characters
            $cleanedOtp = preg_replace('/\D/', '', $otp);
            
            $this->merge([
                'otp' => $cleanedOtp
            ]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator(Validator $validator): void
    {
        $validator->after(function (Validator $validator) {
            // Additional custom validation logic can go here
            
            // For phone number validation, ensure it starts with + if it doesn't already
            if ($this->has('phone_number')) {
                $phoneNumber = $this->input('phone_number');
                if (!empty($phoneNumber) && !str_starts_with($phoneNumber, '+')) {
                    // If it's a Saudi number starting with 5, add +966
                    if (str_starts_with($phoneNumber, '5') && strlen($phoneNumber) === 9) {
                        $this->merge(['phone_number' => '+966' . $phoneNumber]);
                    } elseif (str_starts_with($phoneNumber, '966')) {
                        $this->merge(['phone_number' => '+' . $phoneNumber]);
                    }
                }
            }
        });
    }
}
