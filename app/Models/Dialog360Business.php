<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class Dialog360Business extends Model
{
    use HasFactory;

    protected $table = 'dialog360_businesses';

    protected $fillable = [
        'channel_id',
        'client_id',
        'project_id',
        'partner_id',
        'business_name',
        'phone_number',
        'status',
        'business_created_at',
        'last_activity_at',
        'total_messages',
        'total_free_messages',
        'total_paid_messages',
        'total_revenue',
        'business_initiated_messages',
        'user_initiated_messages',
        'authentication_messages',
        'marketing_messages',
        'service_messages',
        'utility_messages',
        'business_initiated_revenue',
        'user_initiated_revenue',
        'authentication_revenue',
        'marketing_revenue',
        'service_revenue',
        'utility_revenue',
        'average_cost_per_message',
        'free_to_paid_ratio',
        'active_days',
        'first_message_date',
        'last_message_date',
        'messages_last_30_days',
        'revenue_last_30_days',
        'growth_rate_messages',
        'growth_rate_revenue',
        'business_category',
        'usage_pattern',
        'revenue_tier',
        'raw_channel_data',
        'analytics_metadata',
        'last_sync_at',
        'sync_count',
        'is_active'
    ];

    protected $casts = [
        'business_created_at' => 'datetime',
        'last_activity_at' => 'datetime',
        'total_revenue' => 'decimal:6',
        'business_initiated_revenue' => 'decimal:6',
        'user_initiated_revenue' => 'decimal:6',
        'authentication_revenue' => 'decimal:6',
        'marketing_revenue' => 'decimal:6',
        'service_revenue' => 'decimal:6',
        'utility_revenue' => 'decimal:6',
        'average_cost_per_message' => 'decimal:6',
        'free_to_paid_ratio' => 'decimal:2',
        'growth_rate_messages' => 'decimal:2',
        'growth_rate_revenue' => 'decimal:2',
        'revenue_last_30_days' => 'decimal:6',
        'first_message_date' => 'date',
        'last_message_date' => 'date',
        'raw_channel_data' => 'array',
        'analytics_metadata' => 'array',
        'last_sync_at' => 'datetime',
        'is_active' => 'boolean'
    ];

    /**
     * Relationship with traffic data
     */
    public function trafficData()
    {
        return $this->hasMany(Dialog360TrafficData::class, 'channel_id', 'channel_id');
    }

    /**
     * Get recent traffic data
     */
    public function recentTrafficData($days = 30)
    {
        return $this->trafficData()
            ->where('period_date', '>=', Carbon::now()->subDays($days))
            ->orderBy('period_date', 'desc');
    }

    /**
     * Calculate and update business metrics from traffic data
     */
    public function updateMetricsFromTrafficData()
    {
        $trafficData = $this->trafficData()->get();

        if ($trafficData->isEmpty()) {
            return;
        }

        // Calculate totals
        $this->total_messages = $trafficData->sum('quantity');
        $this->total_free_messages = $trafficData->sum('free_quantity');
        $this->total_paid_messages = $trafficData->sum('paid_quantity');
        $this->total_revenue = $trafficData->sum('total_price');

        // Calculate message type breakdowns
        $this->business_initiated_messages = $trafficData->sum('business_initiated_quantity');
        $this->user_initiated_messages = $trafficData->sum('user_initiated_quantity');
        $this->authentication_messages = $trafficData->sum('authentication_quantity');
        $this->marketing_messages = $trafficData->sum('marketing_quantity');
        $this->service_messages = $trafficData->sum('service_quantity');
        $this->utility_messages = $trafficData->sum('utility_quantity');

        // Calculate revenue breakdowns
        $this->business_initiated_revenue = $trafficData->sum('business_initiated_price');
        $this->user_initiated_revenue = $trafficData->sum('user_initiated_price');
        $this->authentication_revenue = $trafficData->sum('authentication_price');
        $this->marketing_revenue = $trafficData->sum('marketing_price');
        $this->service_revenue = $trafficData->sum('service_price');
        $this->utility_revenue = $trafficData->sum('utility_price');

        // Calculate analytics
        $this->average_cost_per_message = $this->total_messages > 0 ?
            $this->total_revenue / $this->total_messages : 0;

        $this->free_to_paid_ratio = $this->total_messages > 0 ?
            ($this->total_free_messages / $this->total_messages) * 100 : 0;

        // Calculate date ranges
        $this->first_message_date = $trafficData->min('period_date');
        $this->last_message_date = $trafficData->max('period_date');
        $this->active_days = $trafficData->distinct('period_date')->count();

        // Calculate last 30 days metrics
        $recent30Days = $trafficData->where('period_date', '>=', Carbon::now()->subDays(30));
        $this->messages_last_30_days = $recent30Days->sum('quantity');
        $this->revenue_last_30_days = $recent30Days->sum('total_price');

        // Calculate growth rates (compare last 30 days vs previous 30 days)
        $previous30Days = $trafficData->whereBetween('period_date', [
            Carbon::now()->subDays(60),
            Carbon::now()->subDays(30)
        ]);

        $previousMessages = $previous30Days->sum('quantity');
        $previousRevenue = $previous30Days->sum('total_price');

        $this->growth_rate_messages = $previousMessages > 0 ?
            (($this->messages_last_30_days - $previousMessages) / $previousMessages) * 100 : 0;

        $this->growth_rate_revenue = $previousRevenue > 0 ?
            (($this->revenue_last_30_days - $previousRevenue) / $previousRevenue) * 100 : 0;

        // Auto-categorize business
        $this->categorizeBusinessUsage();

        // Update sync info
        $this->last_sync_at = now();
        $this->sync_count++;

        $this->save();
    }

    /**
     * Auto-categorize business based on usage patterns
     */
    protected function categorizeBusinessUsage()
    {
        // Determine usage pattern
        if ($this->messages_last_30_days > 10000) {
            $this->usage_pattern = 'heavy';
        } elseif ($this->messages_last_30_days > 1000) {
            $this->usage_pattern = 'moderate';
        } elseif ($this->messages_last_30_days > 0) {
            $this->usage_pattern = 'light';
        } else {
            $this->usage_pattern = 'inactive';
        }

        // Determine revenue tier
        if ($this->revenue_last_30_days > 1000) {
            $this->revenue_tier = 'high';
        } elseif ($this->revenue_last_30_days > 100) {
            $this->revenue_tier = 'medium';
        } else {
            $this->revenue_tier = 'low';
        }

        // Determine business category based on message types
        $messageTypes = [
            'marketing' => $this->marketing_messages,
            'service' => $this->service_messages,
            'authentication' => $this->authentication_messages,
            'utility' => $this->utility_messages
        ];

        $dominantType = array_keys($messageTypes, max($messageTypes))[0];
        $this->business_category = $dominantType;
    }

    /**
     * Get business insights
     */
    public static function getBusinessInsights()
    {
        return [
            'total_businesses' => static::count(),
            'active_businesses' => static::where('status', 'ready')->count(),
            'total_revenue' => static::sum('total_revenue'),
            'total_messages' => static::sum('total_messages'),
            'average_revenue_per_business' => static::avg('total_revenue'),
            'top_performers' => static::orderBy('total_revenue', 'desc')->limit(10)->get(),
            'usage_patterns' => static::selectRaw('usage_pattern, COUNT(*) as count')
                ->groupBy('usage_pattern')
                ->get(),
            'revenue_tiers' => static::selectRaw('revenue_tier, COUNT(*) as count')
                ->groupBy('revenue_tier')
                ->get(),
            'business_categories' => static::selectRaw('business_category, COUNT(*) as count')
                ->groupBy('business_category')
                ->get()
        ];
    }

    /**
     * Scope for active businesses
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('status', 'ready');
    }

    /**
     * Scope for businesses with recent activity
     */
    public function scopeRecentlyActive($query, $days = 30)
    {
        return $query->where('last_message_date', '>=', Carbon::now()->subDays($days));
    }
}
