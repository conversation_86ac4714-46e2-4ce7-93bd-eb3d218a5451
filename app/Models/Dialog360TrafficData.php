<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Dialog360TrafficData extends Model
{
    use HasFactory;

    protected $table = 'dialog360_traffic_data';

    protected $fillable = [
        'channel_id',
        'client_id',
        'project_id',
        'partner_id',
        'client_name',
        'phone_number',
        'status',
        'period_date',
        'quantity',
        'free_quantity',
        'paid_quantity',
        'free_tier',
        'free_entry_point',
        'business_initiated_quantity',
        'business_initiated_paid_quantity',
        'business_initiated_price',
        'user_initiated_quantity',
        'user_initiated_paid_quantity',
        'user_initiated_price',
        'authentication_quantity',
        'authentication_paid_quantity',
        'authentication_price',
        'marketing_quantity',
        'marketing_paid_quantity',
        'marketing_price',
        'service_quantity',
        'service_paid_quantity',
        'service_price',
        'utility_quantity',
        'utility_paid_quantity',
        'utility_price',
        'total_price',
        'raw_data',
        'imported_at'
    ];

    protected $casts = [
        'period_date' => 'date',
        'business_initiated_price' => 'decimal:6',
        'user_initiated_price' => 'decimal:6',
        'authentication_price' => 'decimal:6',
        'marketing_price' => 'decimal:6',
        'service_price' => 'decimal:6',
        'utility_price' => 'decimal:6',
        'total_price' => 'decimal:6',
        'raw_data' => 'array',
        'imported_at' => 'datetime'
    ];

    /**
     * Get traffic data for a specific channel
     */
    public static function getChannelTraffic($channelId, $startDate = null, $endDate = null)
    {
        $query = static::where('channel_id', $channelId);

        if ($startDate) {
            $query->where('period_date', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('period_date', '<=', $endDate);
        }

        return $query->orderBy('period_date', 'desc')->get();
    }

    /**
     * Get aggregated traffic data for insights
     */
    public static function getTrafficInsights($startDate = null, $endDate = null)
    {
        $query = static::query();

        if ($startDate) {
            $query->where('period_date', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('period_date', '<=', $endDate);
        }

        return $query->selectRaw('
            client_name,
            channel_id,
            COUNT(*) as periods_count,
            SUM(quantity) as total_messages,
            SUM(free_quantity) as total_free,
            SUM(paid_quantity) as total_paid,
            SUM(total_price) as total_cost,
            AVG(total_price) as avg_cost_per_period,
            MAX(period_date) as latest_period,
            MIN(period_date) as earliest_period
        ')
        ->groupBy('client_name', 'channel_id')
        ->orderBy('total_messages', 'desc')
        ->get();
    }

    /**
     * Get monthly aggregated data
     */
    public static function getMonthlyAggregates($year = null)
    {
        $query = static::query();

        if ($year) {
            $query->whereYear('period_date', $year);
        }

        return $query->selectRaw('
            YEAR(period_date) as year,
            MONTH(period_date) as month,
            COUNT(DISTINCT channel_id) as active_channels,
            SUM(quantity) as total_messages,
            SUM(free_quantity) as total_free,
            SUM(paid_quantity) as total_paid,
            SUM(total_price) as total_cost
        ')
        ->groupBy('year', 'month')
        ->orderBy('year', 'desc')
        ->orderBy('month', 'desc')
        ->get();
    }
}
