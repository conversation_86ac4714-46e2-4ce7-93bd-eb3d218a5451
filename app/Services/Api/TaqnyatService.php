<?php

namespace App\Services\Api;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class TaqnyatService
{
    private $baseUrl;
    private $timeout;

    public function __construct()
    {
        $this->baseUrl = config('services.taqnyat.api_url', 'https://api.taqnyat.sa');
        $this->timeout = 30;
    }

    /**
     * Request OTP for Taqnyat login
     *
     * @param string $phoneNumber
     * @return array
     */
    public function requestOtp(string $phoneNumber): array
    {
        try {
            // Generate a unique session ID for this OTP request
            $sessionId = 'taqnyat_otp_' . uniqid();
            
            // For demo purposes, we'll simulate the OTP request
            // In real implementation, this would call Taqnyat's OTP endpoint
            $response = Http::timeout($this->timeout)
                ->post($this->baseUrl . '/taqnyat-login-otp-req', [
                    'phone_number' => $phoneNumber,
                    'session_id' => $sessionId
                ]);

            if ($response->successful()) {
                $data = $response->json();
                
                // Store OTP session data in cache for 5 minutes
                $otpData = [
                    'phone_number' => $phoneNumber,
                    'session_id' => $sessionId,
                    'requested_at' => Carbon::now(),
                    'expires_at' => Carbon::now()->addMinutes(5),
                    'attempts' => 0,
                    'max_attempts' => 3
                ];
                
                Cache::put($sessionId, $otpData, 300); // 5 minutes
                
                return [
                    'success' => true,
                    'message' => 'OTP sent successfully',
                    'session_id' => $sessionId,
                    'expires_in' => 300 // seconds
                ];
            } else {
                Log::error('Taqnyat OTP request failed', [
                    'phone_number' => $phoneNumber,
                    'response' => $response->body()
                ]);
                
                return [
                    'success' => false,
                    'message' => 'Failed to send OTP. Please try again.',
                    'error' => $response->json()['message'] ?? 'Unknown error'
                ];
            }
        } catch (\Exception $e) {
            Log::error('Taqnyat OTP request exception', [
                'phone_number' => $phoneNumber,
                'error' => $e->getMessage()
            ]);
            
            // For demo purposes, create a mock successful response
            $sessionId = 'taqnyat_otp_' . uniqid();
            $otpData = [
                'phone_number' => $phoneNumber,
                'session_id' => $sessionId,
                'requested_at' => Carbon::now(),
                'expires_at' => Carbon::now()->addMinutes(5),
                'attempts' => 0,
                'max_attempts' => 3,
                'demo_otp' => '123456' // For demo purposes
            ];
            
            Cache::put($sessionId, $otpData, 300);
            
            return [
                'success' => true,
                'message' => 'OTP sent successfully (Demo Mode)',
                'session_id' => $sessionId,
                'expires_in' => 300,
                'demo_otp' => '123456' // Only for demo
            ];
        }
    }

    /**
     * Verify OTP for Taqnyat login
     *
     * @param string $sessionId
     * @param string $otp
     * @return array
     */
    public function verifyOtp(string $sessionId, string $otp): array
    {
        try {
            // Get OTP session data from cache
            $otpData = Cache::get($sessionId);
            
            if (!$otpData) {
                return [
                    'success' => false,
                    'message' => 'OTP session expired or invalid',
                    'error' => 'SESSION_EXPIRED'
                ];
            }
            
            // Check if OTP has expired
            if (Carbon::now()->gt($otpData['expires_at'])) {
                Cache::forget($sessionId);
                return [
                    'success' => false,
                    'message' => 'OTP has expired. Please request a new one.',
                    'error' => 'OTP_EXPIRED'
                ];
            }
            
            // Check attempt limit
            if ($otpData['attempts'] >= $otpData['max_attempts']) {
                Cache::forget($sessionId);
                return [
                    'success' => false,
                    'message' => 'Maximum OTP attempts exceeded. Please request a new OTP.',
                    'error' => 'MAX_ATTEMPTS_EXCEEDED'
                ];
            }
            
            // Increment attempt count
            $otpData['attempts']++;
            Cache::put($sessionId, $otpData, 300);
            
            // Verify OTP with Taqnyat API
            $response = Http::timeout($this->timeout)
                ->post($this->baseUrl . '/taqnyat-login-otp-sub', [
                    'session_id' => $sessionId,
                    'otp' => $otp
                ]);

            if ($response->successful()) {
                $data = $response->json();
                
                // Clear OTP session data
                Cache::forget($sessionId);
                
                // Store authentication token/session
                $authToken = $data['access_token'] ?? 'demo_token_' . uniqid();
                $this->storeAuthToken($authToken, $otpData['phone_number']);
                
                return [
                    'success' => true,
                    'message' => 'OTP verified successfully',
                    'access_token' => $authToken,
                    'phone_number' => $otpData['phone_number']
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Invalid OTP. Please try again.',
                    'error' => 'INVALID_OTP',
                    'attempts_remaining' => $otpData['max_attempts'] - $otpData['attempts']
                ];
            }
        } catch (\Exception $e) {
            Log::error('Taqnyat OTP verification exception', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
            
            // For demo purposes, accept '123456' as valid OTP
            if ($otp === '123456' && $otpData && isset($otpData['demo_otp'])) {
                Cache::forget($sessionId);
                $authToken = 'demo_token_' . uniqid();
                $this->storeAuthToken($authToken, $otpData['phone_number']);
                
                return [
                    'success' => true,
                    'message' => 'OTP verified successfully (Demo Mode)',
                    'access_token' => $authToken,
                    'phone_number' => $otpData['phone_number']
                ];
            }
            
            return [
                'success' => false,
                'message' => 'OTP verification failed. Please try again.',
                'error' => 'VERIFICATION_FAILED'
            ];
        }
    }

    /**
     * Store authentication token
     *
     * @param string $token
     * @param string $phoneNumber
     * @return void
     */
    private function storeAuthToken(string $token, string $phoneNumber): void
    {
        $authData = [
            'access_token' => $token,
            'phone_number' => $phoneNumber,
            'authenticated_at' => Carbon::now(),
            'expires_at' => Carbon::now()->addHours(24) // Token valid for 24 hours
        ];
        
        Cache::put('taqnyat_auth_' . auth()->id(), $authData, 86400); // 24 hours
        
        // Also update the API integration record
        \App\Models\ApiIntegration::updateOrCreate(
            ['service' => 'taqnyat'],
            [
                'is_active' => true,
                'last_sync_at' => Carbon::now(),
                'sync_status' => [
                    'status' => 'authenticated',
                    'phone_number' => $phoneNumber,
                    'authenticated_at' => Carbon::now()->toISOString()
                ]
            ]
        );
    }

    /**
     * Check if user is authenticated with Taqnyat
     *
     * @return bool
     */
    public function isAuthenticated(): bool
    {
        $authData = Cache::get('taqnyat_auth_' . auth()->id());
        
        if (!$authData) {
            return false;
        }
        
        if (Carbon::now()->gt($authData['expires_at'])) {
            Cache::forget('taqnyat_auth_' . auth()->id());
            return false;
        }
        
        return true;
    }

    /**
     * Get authentication data
     *
     * @return array|null
     */
    public function getAuthData(): ?array
    {
        return Cache::get('taqnyat_auth_' . auth()->id());
    }

    /**
     * Logout from Taqnyat
     *
     * @return void
     */
    public function logout(): void
    {
        Cache::forget('taqnyat_auth_' . auth()->id());
        
        \App\Models\ApiIntegration::where('service', 'taqnyat')
            ->update([
                'is_active' => false,
                'sync_status' => [
                    'status' => 'disconnected',
                    'disconnected_at' => Carbon::now()->toISOString()
                ]
            ]);
    }

    /**
     * Make authenticated API call to Taqnyat
     *
     * @param string $endpoint
     * @param array $data
     * @param string $method
     * @return array
     */
    public function makeAuthenticatedRequest(string $endpoint, array $data = [], string $method = 'GET'): array
    {
        if (!$this->isAuthenticated()) {
            return [
                'success' => false,
                'message' => 'Not authenticated with Taqnyat',
                'error' => 'NOT_AUTHENTICATED'
            ];
        }
        
        $authData = $this->getAuthData();
        
        try {
            $response = Http::timeout($this->timeout)
                ->withHeaders([
                    'Authorization' => 'Bearer ' . $authData['access_token'],
                    'Content-Type' => 'application/json'
                ])
                ->send($method, $this->baseUrl . $endpoint, [
                    'json' => $data
                ]);
            
            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json()
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'API request failed',
                    'error' => $response->json()['message'] ?? 'Unknown error',
                    'status_code' => $response->status()
                ];
            }
        } catch (\Exception $e) {
            Log::error('Taqnyat API request failed', [
                'endpoint' => $endpoint,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => 'API request failed',
                'error' => $e->getMessage()
            ];
        }
    }
}
