<?php

namespace App\Http\Controllers;

use App\Models\Business;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BusinessController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Business::query();

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by lead score
        if ($request->filled('lead_score')) {
            $query->where('lead_score', $request->lead_score);
        }

        // Filter by lead source
        if ($request->filled('lead_source')) {
            $query->where('lead_source', $request->lead_source);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('contact_person', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Sort functionality
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $businesses = $query->paginate(15)->withQueryString();

        // Get unique lead sources for filter dropdown
        $leadSources = Business::whereNotNull('lead_source')
            ->distinct()
            ->pluck('lead_source')
            ->sort();

        return view('businesses.index', compact('businesses', 'leadSources'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $products = Product::active()->get();
        return view('businesses.create', compact('products'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'required|in:lead,active,closed',
            'lead_score' => 'nullable|in:10,30,70,100,lost',
            'lead_source' => 'nullable|string|max:255',
            'contact_person' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'website' => 'nullable|url|max:255',
            'industry' => 'nullable|string|max:255',
            'estimated_value' => 'nullable|numeric|min:0',
            'expected_close_date' => 'nullable|date',
            'notes' => 'nullable|string',
            'products' => 'nullable|array',
            'products.*.id' => 'exists:products,id',
            'products.*.quantity' => 'integer|min:1',
            'products.*.unit_price' => 'nullable|numeric|min:0',
        ]);

        DB::transaction(function() use ($validated) {
            $business = Business::create($validated);

            // Attach products if provided
            if (!empty($validated['products'])) {
                foreach ($validated['products'] as $product) {
                    $business->products()->attach($product['id'], [
                        'quantity' => $product['quantity'],
                        'unit_price' => $product['unit_price'] ?? null,
                    ]);
                }
            }
        });

        return redirect()->route('businesses.index')
            ->with('success', 'Business created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Business $business)
    {
        $business->load(['products', 'invoices', 'documents', 'whatsappTraffic']);
        return view('businesses.show', compact('business'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Business $business)
    {
        $products = Product::active()->get();
        $business->load('products');
        return view('businesses.edit', compact('business', 'products'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Business $business)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'required|in:lead,active,closed',
            'lead_score' => 'nullable|in:10,30,70,100,lost',
            'lead_source' => 'nullable|string|max:255',
            'contact_person' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'website' => 'nullable|url|max:255',
            'industry' => 'nullable|string|max:255',
            'estimated_value' => 'nullable|numeric|min:0',
            'expected_close_date' => 'nullable|date',
            'notes' => 'nullable|string',
            'products' => 'nullable|array',
            'products.*.id' => 'exists:products,id',
            'products.*.quantity' => 'integer|min:1',
            'products.*.unit_price' => 'nullable|numeric|min:0',
        ]);

        DB::transaction(function() use ($business, $validated) {
            $business->update($validated);

            // Sync products
            if (isset($validated['products'])) {
                $syncData = [];
                foreach ($validated['products'] as $product) {
                    $syncData[$product['id']] = [
                        'quantity' => $product['quantity'],
                        'unit_price' => $product['unit_price'] ?? null,
                    ];
                }
                $business->products()->sync($syncData);
            } else {
                $business->products()->detach();
            }
        });

        return redirect()->route('businesses.show', $business)
            ->with('success', 'Business updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Business $business)
    {
        $business->delete();

        return redirect()->route('businesses.index')
            ->with('success', 'Business deleted successfully.');
    }

    /**
     * Update multiple businesses at once
     */
    public function bulkUpdate(Request $request)
    {
        $validated = $request->validate([
            'business_ids' => 'required|array',
            'business_ids.*' => 'exists:businesses,id',
            'action' => 'required|in:update_status,update_lead_score,update_lead_source',
            'status' => 'required_if:action,update_status|in:lead,active,closed',
            'lead_score' => 'required_if:action,update_lead_score|in:10,30,70,100,lost',
            'lead_source' => 'required_if:action,update_lead_source|string|max:255',
        ]);

        $updateData = [];
        switch ($validated['action']) {
            case 'update_status':
                $updateData['status'] = $validated['status'];
                break;
            case 'update_lead_score':
                $updateData['lead_score'] = $validated['lead_score'];
                break;
            case 'update_lead_source':
                $updateData['lead_source'] = $validated['lead_source'];
                break;
        }

        Business::whereIn('id', $validated['business_ids'])->update($updateData);

        return redirect()->route('businesses.index')
            ->with('success', 'Businesses updated successfully.');
    }
}
