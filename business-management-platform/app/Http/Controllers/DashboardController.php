<?php

namespace App\Http\Controllers;

use App\Models\Business;
use App\Models\Invoice;
use App\Models\WhatsappTraffic;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
        // Business statistics
        $totalBusinesses = Business::count();
        $leadBusinesses = Business::where('status', 'lead')->count();
        $activeBusinesses = Business::where('status', 'active')->count();
        $closedBusinesses = Business::where('status', 'closed')->count();

        // Lead score distribution
        $leadScoreStats = Business::where('status', 'lead')
            ->select('lead_score', DB::raw('count(*) as count'))
            ->groupBy('lead_score')
            ->pluck('count', 'lead_score')
            ->toArray();

        // Recent businesses
        $recentBusinesses = Business::latest()
            ->take(5)
            ->get();

        // Invoice statistics
        $totalInvoices = Invoice::count();
        $paidInvoices = Invoice::where('status', 'paid')->count();
        $pendingInvoices = Invoice::where('status', 'sent')->count();
        $overdueInvoices = Invoice::where('status', 'overdue')
            ->orWhere(function($q) {
                $q->where('status', 'sent')
                  ->where('due_date', '<', now());
            })->count();

        // Revenue statistics
        $totalRevenue = Invoice::where('status', 'paid')->sum('total_amount');
        $monthlyRevenue = Invoice::where('status', 'paid')
            ->whereMonth('paid_at', now()->month)
            ->whereYear('paid_at', now()->year)
            ->sum('total_amount');

        // WhatsApp traffic statistics
        $totalMessages = WhatsappTraffic::count();
        $todayMessages = WhatsappTraffic::whereDate('sent_at', today())->count();
        $inboundMessages = WhatsappTraffic::where('direction', 'inbound')->count();
        $outboundMessages = WhatsappTraffic::where('direction', 'outbound')->count();

        // Monthly business creation trend
        $monthlyBusinesses = Business::select(
            DB::raw('MONTH(created_at) as month'),
            DB::raw('COUNT(*) as count')
        )
        ->whereYear('created_at', now()->year)
        ->groupBy('month')
        ->orderBy('month')
        ->pluck('count', 'month')
        ->toArray();

        // Fill missing months with 0
        for ($i = 1; $i <= 12; $i++) {
            if (!isset($monthlyBusinesses[$i])) {
                $monthlyBusinesses[$i] = 0;
            }
        }
        ksort($monthlyBusinesses);

        // Lead source distribution
        $leadSources = Business::where('status', 'lead')
            ->whereNotNull('lead_source')
            ->select('lead_source', DB::raw('count(*) as count'))
            ->groupBy('lead_source')
            ->orderByDesc('count')
            ->take(5)
            ->pluck('count', 'lead_source')
            ->toArray();

        return view('dashboard', compact(
            'totalBusinesses',
            'leadBusinesses',
            'activeBusinesses',
            'closedBusinesses',
            'leadScoreStats',
            'recentBusinesses',
            'totalInvoices',
            'paidInvoices',
            'pendingInvoices',
            'overdueInvoices',
            'totalRevenue',
            'monthlyRevenue',
            'totalMessages',
            'todayMessages',
            'inboundMessages',
            'outboundMessages',
            'monthlyBusinesses',
            'leadSources'
        ));
    }
}
