<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Business extends Model
{
    protected $fillable = [
        'name',
        'description',
        'status',
        'lead_score',
        'lead_source',
        'contact_person',
        'email',
        'phone',
        'address',
        'website',
        'industry',
        'estimated_value',
        'expected_close_date',
        'notes',
        'taqnyat_data',
        'dialog360_data',
        'is_synced',
        'last_sync_at',
    ];

    protected $casts = [
        'taqnyat_data' => 'array',
        'dialog360_data' => 'array',
        'estimated_value' => 'decimal:2',
        'expected_close_date' => 'date',
        'last_sync_at' => 'datetime',
        'is_synced' => 'boolean',
    ];

    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class)
            ->withPivot(['quantity', 'unit_price', 'notes'])
            ->withTimestamps();
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    public function documents(): HasMany
    {
        return $this->hasMany(Document::class);
    }

    public function whatsappTraffic(): HasMany
    {
        return $this->hasMany(WhatsappTraffic::class);
    }

    public function scopeLeads($query)
    {
        return $query->where('status', 'lead');
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeClosed($query)
    {
        return $query->where('status', 'closed');
    }

    public function getStatusBadgeAttribute()
    {
        return match($this->status) {
            'lead' => 'bg-yellow-100 text-yellow-800',
            'active' => 'bg-green-100 text-green-800',
            'closed' => 'bg-gray-100 text-gray-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    public function getLeadScoreBadgeAttribute()
    {
        return match($this->lead_score) {
            '10' => 'bg-red-100 text-red-800',
            '30' => 'bg-orange-100 text-orange-800',
            '70' => 'bg-blue-100 text-blue-800',
            '100' => 'bg-green-100 text-green-800',
            'lost' => 'bg-gray-100 text-gray-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }
}
