<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_traffic', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            $table->string('phone_number');
            $table->enum('message_type', ['text', 'image', 'document', 'audio', 'video', 'template']);
            $table->enum('direction', ['inbound', 'outbound']);
            $table->enum('status', ['sent', 'delivered', 'read', 'failed']);
            $table->text('message_content')->nullable();
            $table->json('metadata')->nullable();
            $table->string('message_id')->nullable();
            $table->string('conversation_id')->nullable();
            $table->timestamp('sent_at');
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->json('dialog360_data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_traffic');
    }
};
