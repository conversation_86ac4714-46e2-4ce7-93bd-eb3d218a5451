{"Commands:": "Commandes :", "Options:": "Options :", "Examples:": "Exemples :", "boolean": "booléen", "count": "compteur", "string": "cha<PERSON><PERSON> de caractères", "number": "nombre", "array": "tableau", "required": "requis", "default": "<PERSON><PERSON><PERSON><PERSON>", "default:": "défaut :", "choices:": "choix :", "aliases:": "alias :", "generated-value": "valeur <PERSON><PERSON>", "Not enough non-option arguments: got %s, need at least %s": {"one": "Pas assez d'arguments (hors options) : reçu %s, besoin d'au moins %s", "other": "Pas assez d'arguments (hors options) : reçus %s, besoin d'au moins %s"}, "Too many non-option arguments: got %s, maximum of %s": {"one": "Trop d'arguments (hors options) : reçu %s, maximum de %s", "other": "Trop d'arguments (hors options) : reçus %s, maximum de %s"}, "Missing argument value: %s": {"one": "Argument manquant : %s", "other": "Arguments manquants : %s"}, "Missing required argument: %s": {"one": "Argument requis manquant : %s", "other": "Arguments requis manquants : %s"}, "Unknown argument: %s": {"one": "Argument inconnu : %s", "other": "Arguments inconnus : %s"}, "Unknown command: %s": {"one": "Commande inconnue : %s", "other": "Commandes inconnues : %s"}, "Invalid values:": "Valeurs invalides :", "Argument: %s, Given: %s, Choices: %s": "Argument : %s, donné : %s, choix : %s", "Argument check failed: %s": "Echec de la vérification de l'argument : %s", "Implications failed:": "Arguments dépendants manquants :", "Not enough arguments following: %s": "Pas assez d'arguments après : %s", "Invalid JSON config file: %s": "Fichier de configuration JSON invalide : %s", "Path to JSON config file": "Chemin du fichier de configuration JSON", "Show help": "Affiche l'aide", "Show version number": "Affiche le numéro de version", "Did you mean %s?": "Vouliez-vous dire %s ?", "Arguments %s and %s are mutually exclusive": "Les arguments %s et %s sont mutuellement exclusifs", "Positionals:": "Arguments positionnels :", "command": "commande"}