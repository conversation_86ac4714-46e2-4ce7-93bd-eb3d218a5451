<x-admin-layout>
    <x-slot name="header">
        <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p class="text-gray-600">Welcome back! Here's what's happening with your business.</p>
    </x-slot>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Businesses -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-building text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Businesses</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $totalBusinesses }}</p>
                </div>
            </div>
        </div>

        <!-- Lead Businesses -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <i class="fas fa-user-plus text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Leads</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $leadBusinesses }}</p>
                </div>
            </div>
        </div>

        <!-- Active Businesses -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-check-circle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $activeBusinesses }}</p>
                </div>
            </div>
        </div>

        <!-- Revenue -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                    <i class="fas fa-dollar-sign text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($totalRevenue, 2) }} SAR</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Lead Score Distribution -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Lead Score Distribution</h3>
            <div class="space-y-3">
                @foreach(['10' => 'Low (10)', '30' => 'Medium (30)', '70' => 'High (70)', '100' => 'Hot (100)', 'lost' => 'Lost'] as $score => $label)
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">{{ $label }}</span>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                @php
                                    $count = $leadScoreStats[$score] ?? 0;
                                    $percentage = $leadBusinesses > 0 ? ($count / $leadBusinesses) * 100 : 0;
                                @endphp
                                <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $percentage }}%"></div>
                            </div>
                            <span class="text-sm font-medium text-gray-900">{{ $count }}</span>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Recent Businesses -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Businesses</h3>
            <div class="space-y-3">
                @forelse($recentBusinesses as $business)
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <p class="font-medium text-gray-900">{{ $business->name }}</p>
                            <p class="text-sm text-gray-600">{{ $business->contact_person }}</p>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $business->status_badge }}">
                                {{ ucfirst($business->status) }}
                            </span>
                            @if($business->lead_score)
                                <p class="text-xs text-gray-500 mt-1">Score: {{ $business->lead_score }}</p>
                            @endif
                        </div>
                    </div>
                @empty
                    <p class="text-gray-500 text-center py-4">No businesses found</p>
                @endforelse
            </div>
            <div class="mt-4">
                <a href="{{ route('businesses.index') }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    View all businesses →
                </a>
            </div>
        </div>
    </div>

    <!-- Additional Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Invoice Statistics -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Invoices</h3>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-gray-600">Total</span>
                    <span class="font-medium">{{ $totalInvoices }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Paid</span>
                    <span class="font-medium text-green-600">{{ $paidInvoices }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Pending</span>
                    <span class="font-medium text-yellow-600">{{ $pendingInvoices }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Overdue</span>
                    <span class="font-medium text-red-600">{{ $overdueInvoices }}</span>
                </div>
            </div>
        </div>

        <!-- WhatsApp Traffic -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">WhatsApp Traffic</h3>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-gray-600">Total Messages</span>
                    <span class="font-medium">{{ $totalMessages }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Today</span>
                    <span class="font-medium">{{ $todayMessages }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Inbound</span>
                    <span class="font-medium text-blue-600">{{ $inboundMessages }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Outbound</span>
                    <span class="font-medium text-green-600">{{ $outboundMessages }}</span>
                </div>
            </div>
        </div>

        <!-- Lead Sources -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Lead Sources</h3>
            <div class="space-y-2">
                @forelse($leadSources as $source => $count)
                    <div class="flex justify-between">
                        <span class="text-gray-600">{{ $source }}</span>
                        <span class="font-medium">{{ $count }}</span>
                    </div>
                @empty
                    <p class="text-gray-500 text-center py-2">No lead sources</p>
                @endforelse
            </div>
        </div>
    </div>
</x-admin-layout>
