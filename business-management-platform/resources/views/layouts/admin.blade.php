<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Laravel') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Tailwind CSS CDN -->
        <script src="https://cdn.tailwindcss.com"></script>
        
        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
        
        <script>
            tailwind.config = {
                theme: {
                    extend: {
                        colors: {
                            primary: {
                                50: '#eff6ff',
                                500: '#3b82f6',
                                600: '#2563eb',
                                700: '#1d4ed8',
                            }
                        }
                    }
                }
            }
        </script>
    </head>
    <body class="font-sans antialiased bg-gray-50">
        <div class="flex h-screen">
            <!-- Sidebar -->
            <div class="w-64 bg-white shadow-lg">
                <div class="flex items-center justify-center h-16 border-b border-gray-200">
                    <h1 class="text-xl font-bold text-gray-800">
                        <i class="fas fa-chart-line text-primary-600 mr-2"></i>
                        Business Platform
                    </h1>
                </div>
                
                <nav class="mt-8">
                    <div class="px-4 space-y-2">
                        <!-- Dashboard -->
                        <a href="{{ route('dashboard') }}" 
                           class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 {{ request()->routeIs('dashboard') ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-600' : '' }}">
                            <i class="fas fa-tachometer-alt w-5 h-5 mr-3"></i>
                            Dashboard
                        </a>

                        <!-- Taqnyat Section -->
                        <div class="pt-4">
                            <h3 class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">Taqnyat</h3>
                            <div class="mt-2 space-y-1">
                                <a href="{{ route('taqnyat.account') }}" 
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100 {{ request()->routeIs('taqnyat.account') ? 'bg-primary-50 text-primary-700' : '' }}">
                                    <i class="fas fa-user-circle w-4 h-4 mr-3"></i>
                                    Account
                                </a>
                                <a href="{{ route('taqnyat.crm') }}" 
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100 {{ request()->routeIs('taqnyat.crm') ? 'bg-primary-50 text-primary-700' : '' }}">
                                    <i class="fas fa-users w-4 h-4 mr-3"></i>
                                    CRM
                                </a>
                                <a href="{{ route('taqnyat.invoice') }}" 
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100 {{ request()->routeIs('taqnyat.invoice') ? 'bg-primary-50 text-primary-700' : '' }}">
                                    <i class="fas fa-file-invoice w-4 h-4 mr-3"></i>
                                    Invoice
                                </a>
                                <a href="{{ route('taqnyat.products') }}" 
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100 {{ request()->routeIs('taqnyat.products') ? 'bg-primary-50 text-primary-700' : '' }}">
                                    <i class="fas fa-box w-4 h-4 mr-3"></i>
                                    Products
                                </a>
                            </div>
                        </div>

                        <!-- 360Dialog Section -->
                        <div class="pt-4">
                            <h3 class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">360Dialog</h3>
                            <div class="mt-2 space-y-1">
                                <a href="{{ route('360dialog.accounts') }}" 
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100 {{ request()->routeIs('360dialog.accounts') ? 'bg-primary-50 text-primary-700' : '' }}">
                                    <i class="fas fa-user-friends w-4 h-4 mr-3"></i>
                                    Accounts
                                </a>
                                <a href="{{ route('360dialog.traffic') }}" 
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100 {{ request()->routeIs('360dialog.traffic') ? 'bg-primary-50 text-primary-700' : '' }}">
                                    <i class="fab fa-whatsapp w-4 h-4 mr-3"></i>
                                    Traffic
                                </a>
                            </div>
                        </div>

                        <!-- Businesses -->
                        <div class="pt-4">
                            <a href="{{ route('businesses.index') }}" 
                               class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 {{ request()->routeIs('businesses.*') ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-600' : '' }}">
                                <i class="fas fa-building w-5 h-5 mr-3"></i>
                                Businesses
                            </a>
                        </div>

                        <!-- Reports -->
                        <a href="{{ route('reports.index') }}" 
                           class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 {{ request()->routeIs('reports.*') ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-600' : '' }}">
                            <i class="fas fa-chart-bar w-5 h-5 mr-3"></i>
                            Reports
                        </a>

                        <!-- Settings Section -->
                        <div class="pt-4">
                            <h3 class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">Settings</h3>
                            <div class="mt-2 space-y-1">
                                <a href="{{ route('settings.taqnyat') }}" 
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100 {{ request()->routeIs('settings.taqnyat') ? 'bg-primary-50 text-primary-700' : '' }}">
                                    <i class="fas fa-cog w-4 h-4 mr-3"></i>
                                    Taqnyat
                                </a>
                                <a href="{{ route('settings.360dialog') }}" 
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100 {{ request()->routeIs('settings.360dialog') ? 'bg-primary-50 text-primary-700' : '' }}">
                                    <i class="fas fa-cog w-4 h-4 mr-3"></i>
                                    360Dialog
                                </a>
                            </div>
                        </div>
                    </div>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="flex-1 flex flex-col overflow-hidden">
                <!-- Top Navigation -->
                <header class="bg-white shadow-sm border-b border-gray-200">
                    <div class="flex items-center justify-between px-6 py-4">
                        <div>
                            @isset($header)
                                {{ $header }}
                            @endisset
                        </div>
                        
                        <div class="flex items-center space-x-4">
                            <!-- Notifications -->
                            <button class="text-gray-500 hover:text-gray-700">
                                <i class="fas fa-bell w-5 h-5"></i>
                            </button>
                            
                            <!-- User Menu -->
                            <div class="relative" x-data="{ open: false }">
                                <button @click="open = !open" class="flex items-center text-sm text-gray-700 hover:text-gray-900">
                                    <img class="w-8 h-8 rounded-full mr-2" src="https://ui-avatars.com/api/?name={{ urlencode(auth()->user()->name) }}&background=3b82f6&color=fff" alt="{{ auth()->user()->name }}">
                                    <span>{{ auth()->user()->name }}</span>
                                    <i class="fas fa-chevron-down w-3 h-3 ml-2"></i>
                                </button>
                                
                                <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                                    <a href="{{ route('profile.edit') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-user w-4 h-4 mr-2"></i>
                                        Profile
                                    </a>
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-sign-out-alt w-4 h-4 mr-2"></i>
                                            Logout
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </header>

                <!-- Page Content -->
                <main class="flex-1 overflow-y-auto p-6">
                    <!-- Flash Messages -->
                    @if(session('success'))
                        <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline">{{ session('error') }}</span>
                        </div>
                    @endif

                    {{ $slot }}
                </main>
            </div>
        </div>

        <!-- Alpine.js for dropdown functionality -->
        <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    </body>
</html>
