<x-admin-layout>
    <x-slot name="header">
        <h1 class="text-2xl font-bold text-gray-900">Taqnyat Settings</h1>
        <p class="text-gray-600">Configure your Taqnyat API integration</p>
    </x-slot>

    <div class="bg-white rounded-lg shadow p-6">
        <form class="space-y-6">
            @csrf
            
            <!-- API Configuration -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">API Configuration</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">API Key</label>
                        <input type="password" name="api_key" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Enter your Taqnyat API key">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Account ID</label>
                        <input type="text" name="account_id" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Enter your account ID">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">API URL</label>
                        <input type="url" name="api_url" value="https://api.taqnyat.sa"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Environment</label>
                        <select name="environment" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="production">Production</option>
                            <option value="sandbox">Sandbox</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Sync Settings -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Synchronization Settings</h3>
                
                <div class="space-y-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="auto_sync" name="auto_sync" class="rounded border-gray-300">
                        <label for="auto_sync" class="ml-2 text-sm text-gray-700">Enable automatic synchronization</label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="sync_customers" name="sync_customers" class="rounded border-gray-300">
                        <label for="sync_customers" class="ml-2 text-sm text-gray-700">Sync customer data</label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="sync_invoices" name="sync_invoices" class="rounded border-gray-300">
                        <label for="sync_invoices" class="ml-2 text-sm text-gray-700">Sync invoices</label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="sync_products" name="sync_products" class="rounded border-gray-300">
                        <label for="sync_products" class="ml-2 text-sm text-gray-700">Sync products</label>
                    </div>
                </div>
            </div>

            <!-- Webhook Settings -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Webhook Settings</h3>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Webhook URL</label>
                    <input type="url" name="webhook_url" 
                           value="{{ url('/webhooks/taqnyat') }}" readonly
                           class="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-50">
                    <p class="text-xs text-gray-500 mt-1">Use this URL in your Taqnyat webhook configuration</p>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex space-x-4">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium">
                    Save Settings
                </button>
                
                <button type="button" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium">
                    Test Connection
                </button>
                
                <button type="button" class="bg-yellow-600 hover:bg-yellow-700 text-white px-6 py-2 rounded-lg font-medium">
                    Sync Now
                </button>
            </div>
        </form>
    </div>
</x-admin-layout>
