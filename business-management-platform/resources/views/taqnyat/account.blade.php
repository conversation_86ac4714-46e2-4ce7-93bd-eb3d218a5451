<x-admin-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Taqnyat Account</h1>
                <p class="text-gray-600">Manage your Taqnyat account integration</p>
            </div>
            <div class="flex items-center space-x-2">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    <i class="fas fa-times-circle mr-1"></i>
                    Not Connected
                </span>
            </div>
        </div>
    </x-slot>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Connection Status -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Connection Status</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between p-4 bg-red-50 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle text-red-500 mr-3"></i>
                        <div>
                            <p class="font-medium text-red-900">Not Connected</p>
                            <p class="text-sm text-red-700">Configure your API credentials to connect</p>
                        </div>
                    </div>
                </div>
                
                <div class="text-center">
                    <a href="{{ route('settings.taqnyat') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                        <i class="fas fa-cog mr-2"></i>
                        Configure Settings
                    </a>
                </div>
            </div>
        </div>

        <!-- Account Information -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Account Information</h3>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600">Account ID</span>
                    <span class="font-medium text-gray-400">Not Available</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">API Status</span>
                    <span class="font-medium text-red-600">Disconnected</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Last Sync</span>
                    <span class="font-medium text-gray-400">Never</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Plan</span>
                    <span class="font-medium text-gray-400">Unknown</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Overview -->
    <div class="mt-8 bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Available Features</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="text-center p-4 border border-gray-200 rounded-lg">
                <i class="fas fa-users text-3xl text-blue-600 mb-2"></i>
                <h4 class="font-medium text-gray-900">CRM Integration</h4>
                <p class="text-sm text-gray-600">Sync customer data</p>
            </div>
            <div class="text-center p-4 border border-gray-200 rounded-lg">
                <i class="fas fa-file-invoice text-3xl text-green-600 mb-2"></i>
                <h4 class="font-medium text-gray-900">Invoice Management</h4>
                <p class="text-sm text-gray-600">Create and manage invoices</p>
            </div>
            <div class="text-center p-4 border border-gray-200 rounded-lg">
                <i class="fas fa-box text-3xl text-purple-600 mb-2"></i>
                <h4 class="font-medium text-gray-900">Product Catalog</h4>
                <p class="text-sm text-gray-600">Sync product information</p>
            </div>
            <div class="text-center p-4 border border-gray-200 rounded-lg">
                <i class="fas fa-chart-bar text-3xl text-orange-600 mb-2"></i>
                <h4 class="font-medium text-gray-900">Analytics</h4>
                <p class="text-sm text-gray-600">Business insights</p>
            </div>
        </div>
    </div>
</x-admin-layout>
