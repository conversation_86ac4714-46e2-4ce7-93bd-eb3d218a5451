<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\BusinessController;
use App\Http\Controllers\ProductController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect()->route('login');
});

Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware('auth')->group(function () {
    // Profile routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Business management routes
    Route::resource('businesses', BusinessController::class);
    Route::post('/businesses/bulk-update', [BusinessController::class, 'bulkUpdate'])->name('businesses.bulk-update');

    // Product management routes
    Route::resource('products', ProductController::class);

    // Taqnyat routes
    Route::prefix('taqnyat')->name('taqnyat.')->group(function () {
        Route::get('/account', function () {
            return view('taqnyat.account');
        })->name('account');

        Route::get('/crm', function () {
            return view('taqnyat.crm');
        })->name('crm');

        Route::get('/invoice', function () {
            return view('taqnyat.invoice');
        })->name('invoice');

        Route::get('/products', function () {
            return view('taqnyat.products');
        })->name('products');
    });

    // 360Dialog routes
    Route::prefix('360dialog')->name('360dialog.')->group(function () {
        Route::get('/accounts', function () {
            return view('360dialog.accounts');
        })->name('accounts');

        Route::get('/traffic', function () {
            return view('360dialog.traffic');
        })->name('traffic');
    });

    // Reports routes
    Route::get('/reports', function () {
        return view('reports.index');
    })->name('reports.index');

    // Settings routes
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/taqnyat', function () {
            return view('settings.taqnyat');
        })->name('taqnyat');

        Route::get('/360dialog', function () {
            return view('settings.360dialog');
        })->name('360dialog');
    });
});

require __DIR__.'/auth.php';
