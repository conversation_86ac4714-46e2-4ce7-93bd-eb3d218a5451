{"name": "dompdf/php-svg-lib", "type": "library", "description": "A library to read, parse and export to PDF SVG files.", "homepage": "https://github.com/dompdf/php-svg-lib", "license": "LGPL-3.0-or-later", "authors": [{"name": "The SvgLib Community", "homepage": "https://github.com/dompdf/php-svg-lib/blob/master/AUTHORS.md"}], "autoload": {"psr-4": {"Svg\\": "src/Svg"}}, "autoload-dev": {"psr-4": {"Svg\\Tests\\": "tests/Svg"}}, "require": {"php": "^7.1 || ^8.0", "ext-mbstring": "*", "sabberworm/php-css-parser": "^8.4"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.5 || ^9.5"}}