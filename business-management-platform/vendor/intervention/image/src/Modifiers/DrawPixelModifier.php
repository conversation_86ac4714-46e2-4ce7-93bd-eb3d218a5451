<?php

declare(strict_types=1);

namespace Intervention\Image\Modifiers;

use Intervention\Image\Drivers\SpecializableModifier;
use Intervention\Image\Interfaces\PointInterface;

class DrawPixelModifier extends SpecializableModifier
{
    /**
     * Create new modifier object
     *
     * @param PointInterface $position
     * @param mixed $color
     * @return void
     */
    public function __construct(
        public PointInterface $position,
        public mixed $color
    ) {
    }
}
