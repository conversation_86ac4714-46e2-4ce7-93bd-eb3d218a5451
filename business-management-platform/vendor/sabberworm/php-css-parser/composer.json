{"name": "sabberworm/php-css-parser", "type": "library", "description": "Parser for CSS Files written in PHP", "keywords": ["parser", "css", "stylesheet"], "homepage": "https://www.sabberworm.com/blog/2010/6/10/php-css-parser", "license": "MIT", "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^5.6.20 || ^7.0.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "ext-iconv": "*"}, "require-dev": {"phpunit/phpunit": "5.7.27 || 6.5.14 || 7.5.20 || 8.5.41"}, "suggest": {"ext-mbstring": "for parsing UTF-8 CSS"}, "autoload": {"psr-4": {"Sabberworm\\CSS\\": "src/"}}, "autoload-dev": {"psr-4": {"Sabberworm\\CSS\\Tests\\": "tests/"}}, "extra": {"branch-alias": {"dev-main": "9.0.x-dev"}}}