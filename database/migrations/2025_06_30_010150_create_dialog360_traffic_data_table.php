<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dialog360_traffic_data', function (Blueprint $table) {
            $table->id();
            $table->string('channel_id')->index();
            $table->string('client_id')->index();
            $table->string('project_id')->index();
            $table->string('partner_id')->index();
            $table->string('client_name');
            $table->string('phone_number');
            $table->string('status');
            $table->date('period_date')->index();

            // Traffic quantities
            $table->integer('quantity')->default(0);
            $table->integer('free_quantity')->default(0);
            $table->integer('paid_quantity')->default(0);
            $table->integer('free_tier')->default(0);
            $table->integer('free_entry_point')->default(0);

            // Business initiated
            $table->integer('business_initiated_quantity')->default(0);
            $table->integer('business_initiated_paid_quantity')->default(0);
            $table->decimal('business_initiated_price', 10, 6)->default(0);

            // User initiated
            $table->integer('user_initiated_quantity')->default(0);
            $table->integer('user_initiated_paid_quantity')->default(0);
            $table->decimal('user_initiated_price', 10, 6)->default(0);

            // Authentication
            $table->integer('authentication_quantity')->default(0);
            $table->integer('authentication_paid_quantity')->default(0);
            $table->decimal('authentication_price', 10, 6)->default(0);

            // Marketing
            $table->integer('marketing_quantity')->default(0);
            $table->integer('marketing_paid_quantity')->default(0);
            $table->decimal('marketing_price', 10, 6)->default(0);

            // Service
            $table->integer('service_quantity')->default(0);
            $table->integer('service_paid_quantity')->default(0);
            $table->decimal('service_price', 10, 6)->default(0);

            // Utility
            $table->integer('utility_quantity')->default(0);
            $table->integer('utility_paid_quantity')->default(0);
            $table->decimal('utility_price', 10, 6)->default(0);

            // Totals
            $table->decimal('total_price', 10, 6)->default(0);

            // Metadata
            $table->json('raw_data')->nullable(); // Store original API response
            $table->timestamp('imported_at')->useCurrent();
            $table->timestamps();

            // Unique constraint to prevent duplicates
            $table->unique(['channel_id', 'period_date'], 'unique_channel_period');

            // Indexes for analytics
            $table->index(['client_name', 'period_date']);
            $table->index(['status', 'period_date']);
            $table->index(['total_price', 'period_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dialog360_traffic_data');
    }
};
