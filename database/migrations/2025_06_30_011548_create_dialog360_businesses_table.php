<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dialog360_businesses', function (Blueprint $table) {
            $table->id();
            $table->string('channel_id')->unique()->index();
            $table->string('client_id')->index();
            $table->string('project_id')->index();
            $table->string('partner_id')->index();

            // Business Information
            $table->string('business_name');
            $table->string('phone_number');
            $table->string('status')->index();
            $table->timestamp('business_created_at')->nullable();
            $table->timestamp('last_activity_at')->nullable();

            // Aggregated Metrics (calculated from traffic data)
            $table->bigInteger('total_messages')->default(0);
            $table->bigInteger('total_free_messages')->default(0);
            $table->bigInteger('total_paid_messages')->default(0);
            $table->decimal('total_revenue', 12, 6)->default(0);

            // Message Type Breakdowns
            $table->bigInteger('business_initiated_messages')->default(0);
            $table->bigInteger('user_initiated_messages')->default(0);
            $table->bigInteger('authentication_messages')->default(0);
            $table->bigInteger('marketing_messages')->default(0);
            $table->bigInteger('service_messages')->default(0);
            $table->bigInteger('utility_messages')->default(0);

            // Revenue Breakdowns
            $table->decimal('business_initiated_revenue', 12, 6)->default(0);
            $table->decimal('user_initiated_revenue', 12, 6)->default(0);
            $table->decimal('authentication_revenue', 12, 6)->default(0);
            $table->decimal('marketing_revenue', 12, 6)->default(0);
            $table->decimal('service_revenue', 12, 6)->default(0);
            $table->decimal('utility_revenue', 12, 6)->default(0);

            // Analytics Data
            $table->decimal('average_cost_per_message', 8, 6)->default(0);
            $table->decimal('free_to_paid_ratio', 5, 2)->default(0); // Percentage
            $table->integer('active_days')->default(0); // Days with activity
            $table->date('first_message_date')->nullable();
            $table->date('last_message_date')->nullable();

            // Growth Metrics
            $table->bigInteger('messages_last_30_days')->default(0);
            $table->decimal('revenue_last_30_days', 12, 6)->default(0);
            $table->decimal('growth_rate_messages', 8, 2)->default(0); // Percentage
            $table->decimal('growth_rate_revenue', 8, 2)->default(0); // Percentage

            // Business Categories (for insights)
            $table->string('business_category')->nullable(); // Auto-categorized based on usage patterns
            $table->string('usage_pattern')->nullable(); // heavy, moderate, light, inactive
            $table->string('revenue_tier')->nullable(); // high, medium, low

            // Raw Data Storage
            $table->json('raw_channel_data')->nullable(); // Store original channel data
            $table->json('analytics_metadata')->nullable(); // Store additional analytics data

            // Import Tracking
            $table->timestamp('last_sync_at')->nullable();
            $table->integer('sync_count')->default(0);
            $table->boolean('is_active')->default(true);

            $table->timestamps();

            // Indexes for analytics queries
            $table->index(['status', 'total_messages']);
            $table->index(['business_category', 'usage_pattern']);
            $table->index(['revenue_tier', 'total_revenue']);
            $table->index(['last_message_date', 'is_active']);
            $table->index(['business_name', 'phone_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dialog360_businesses');
    }
};
