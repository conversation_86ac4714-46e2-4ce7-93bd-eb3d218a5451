<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'phone' => '+966501234567',
            'password' => bcrypt('password'),
        ]);

        // Create sample businesses
        \App\Models\Business::create([
            'name' => 'Tech Solutions Ltd',
            'description' => 'Software development company',
            'status' => 'lead',
            'lead_score' => '70',
            'lead_source' => 'Website',
            'contact_person' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+966501234567',
            'industry' => 'Technology',
            'estimated_value' => 50000.00,
        ]);

        \App\Models\Business::create([
            'name' => 'Marketing Pro',
            'description' => 'Digital marketing agency',
            'status' => 'active',
            'contact_person' => '<PERSON>',
            'email' => '<EMAIL>',
            'phone' => '+966507654321',
            'industry' => 'Marketing',
        ]);

        // Create sample products
        \App\Models\Product::create([
            'name' => 'CRM Software',
            'description' => 'Customer relationship management system',
            'sku' => 'CRM-001',
            'price' => 299.99,
            'category' => 'Software',
        ]);

        \App\Models\Product::create([
            'name' => 'Marketing Automation',
            'description' => 'Automated marketing campaigns',
            'sku' => 'MKT-001',
            'price' => 199.99,
            'category' => 'Software',
        ]);

        // Create API integration records
        \App\Models\ApiIntegration::create([
            'service' => 'taqnyat',
            'api_url' => 'https://api.taqnyat.sa',
            'is_active' => false,
        ]);

        \App\Models\ApiIntegration::create([
            'service' => '360dialog',
            'api_url' => 'https://waba.360dialog.io',
            'is_active' => false,
        ]);
    }
}
