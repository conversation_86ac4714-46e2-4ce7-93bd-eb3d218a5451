# 360Dialog WhatsApp Business API Integration

This document describes the 360Dialog integration for the Business Management Platform, including JWT Bearer token authentication and WhatsApp messaging capabilities.

## Overview

The 360Dialog integration provides WhatsApp Business API functionality through JWT Bearer token authentication. The system supports:

- JWT Bearer token validation and storage
- WhatsApp message sending
- Template management
- Account information retrieval
- Traffic data synchronization

## Authentication

### JWT Bearer Token Format

360Dialog uses JWT (JSON Web Token) Bearer tokens for authentication. The tokens must follow this structure:

- **Algorithm**: RS256
- **Issuer**: `https://360dialog.eu.auth0.com/`
- **Audience**: Contains `360dialog.io` service URLs
- **Organization**: `taqnyat` (for production environment)
- **Environment**: `production`

### Production JWT Token Example

```
eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ik1EZEZOVFk1UVVVMU9FSXhPRGN3UVVZME9EUTFRVFJDT1RSRU9VUTVNVGhDTURWRk9UUTNPQSJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.THcB-zfXr9rvohMvIBc_u5-UtLu2axsNMIA6ofUCYvbs-L19qLvG2j-mkvtGWSFcw4iTz-lZnzEsJOMYazQ69n853pGALX8e1R5lPqiQBfW7FEtohiiTzlZ-5UMfsJBlyeaj6iWUoJUqaQK-dQ551YYX2LHdGypOffZIv7SobBCHRu9Vu9-HDUFIpn5Wc4g1H682CKw1-hIk6BsZVrt2ChflNdGJYASz2XnFHmg-y5_GNonky03MU9kUfE0esDY_Q32oWryT4in1hkMr4YDNo4L7mtI-eiCVKPE4uMHZ3J4MMKBjGQs2LO1qMQom4k-YIFq4rIkmoZskELoPCD-FCQ
```

### Token Validation

The system validates JWT tokens with the following checks:

1. **Structure Validation**: Ensures exactly 3 parts separated by dots (header.payload.signature)
2. **Character Validation**: Validates base64url encoding characters only
3. **Length Validation**: Minimum 200 characters (typical JWT length)
4. **Header Validation**: Checks for RS256 algorithm and JWT type
5. **Payload Validation**: Validates issuer, audience, and 360Dialog-specific claims

## API Endpoints

### Authentication Endpoints

- `POST /api/360dialog/auth/update-token` - Update Bearer token
- `GET /api/360dialog/auth/status` - Get authentication status
- `POST /api/360dialog/auth/disconnect` - Disconnect from 360Dialog
- `GET /api/360dialog/auth/test` - Test connection

### Service Endpoints

- `GET /api/360dialog/account-info` - Get account information
- `GET /api/360dialog/templates` - Get message templates
- `POST /api/360dialog/sync-templates` - Sync templates from 360Dialog
- `POST /api/360dialog/sync-traffic` - Sync traffic data
- `POST /api/360dialog/send-test-message` - Send test message

## Usage

### Setting Up Authentication

1. Navigate to `/settings/360dialog`
2. Enter your JWT Bearer token in the provided field
3. Click "Save & Validate Token"
4. The system will validate the token structure and test the connection

### Sending Messages

Use the test message functionality to send WhatsApp messages:

```javascript
// Example API call
fetch('/api/360dialog/send-test-message', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': csrfToken
    },
    body: JSON.stringify({
        to: '+************',
        message: 'Hello from 360Dialog!'
    })
});
```

## Configuration

### Environment Variables

Add these to your `.env` file:

```env
DIALOG360_API_URL=https://waba.360dialog.io
DIALOG360_WEBHOOK_SECRET=your_webhook_secret
```

### Service Configuration

The service is configured in `config/services.php`:

```php
'360dialog' => [
    'api_url' => env('DIALOG360_API_URL', 'https://waba.360dialog.io'),
    'webhook_secret' => env('DIALOG360_WEBHOOK_SECRET'),
],
```

## Testing

The integration includes comprehensive tests covering:

- JWT token validation
- Authentication flows
- Message sending
- Error handling
- Token storage and retrieval

Run tests with:

```bash
php artisan test tests/Feature/ApiIntegrationTest.php
```

## Security Features

- **JWT Structure Validation**: Ensures tokens follow proper JWT format
- **Claim Validation**: Validates issuer, audience, and organization claims
- **Secure Storage**: Tokens are encrypted and stored securely in the database
- **Error Handling**: Comprehensive error handling with detailed logging
- **Rate Limiting**: Built-in protection against API abuse

## Troubleshooting

### Common Issues

1. **Invalid Token Format**: Ensure the token is a valid JWT with 3 parts
2. **Authentication Failed**: Check that the token hasn't expired
3. **Network Errors**: Verify API connectivity and endpoints
4. **Permission Errors**: Ensure the token has required permissions

### Logs

Check the Laravel logs for detailed error information:

```bash
tail -f storage/logs/laravel.log | grep "360Dialog"
```

## Support

For issues with the 360Dialog integration, check:

1. Laravel application logs
2. 360Dialog dashboard for token status
3. Network connectivity to 360Dialog APIs
4. Token expiration and permissions
