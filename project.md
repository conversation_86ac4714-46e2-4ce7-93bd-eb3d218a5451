Create a laravel app using tailwind css cdn jit and db sqlite and font awsome 

the objective from this app to create a buisness managment platform 

some businesses are [Lead , Active , closed]

what is the business information 

whats is the products the business use 

traffic of the business for whatsapp 

invoices of the business

documents of the business 

insights for the business 

insights for the businesses

connect the business with taqnyat and 360dialog if possible

store data locally and sync 

for the business if its lead it can be [10 , 30 , 70 , 100 , lost]

also i want to sort the lead source based on column 

the app will contain from sidebar with main items 

Dashboard
Taqnyat 
    Account
    CRM
    Invoice
    Products
360Dialog
    Accounts
    Traffic
Businesses
    
Reports

Settings
    Taqnyat
    360Dialog
    