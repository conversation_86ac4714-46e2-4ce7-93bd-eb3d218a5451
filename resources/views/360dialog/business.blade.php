<x-admin-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    {{ __('Business Intelligence Hub') }}
                </h2>
                <p class="text-sm text-gray-600 mt-1">Comprehensive business analytics and management</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="importAllBusinesses()" class="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-4 py-2 rounded-lg shadow-lg transition-all duration-200 transform hover:scale-105">
                    <i class="fas fa-cloud-download-alt mr-2"></i>
                    Import All Businesses
                </button>
                <button onclick="refreshData()" class="bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 text-white px-4 py-2 rounded-lg shadow-lg transition-all duration-200 transform hover:scale-105">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Refresh
                </button>
            </div>
        </div>
    </x-slot>

    <!-- Loading State -->
    <div id="loading-state" class="flex items-center justify-center py-20">
        <div class="text-center">
            <div class="relative">
                <div class="w-20 h-20 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto"></div>
                <div class="absolute inset-0 flex items-center justify-center">
                    <i class="fas fa-building text-blue-600 text-lg"></i>
                </div>
            </div>
            <p class="text-gray-600 mt-4 text-lg">Loading business data...</p>
            <div class="mt-2">
                <div class="bg-gray-200 rounded-full h-2 w-64 mx-auto">
                    <div id="loading-progress" class="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
                <p id="loading-text" class="text-sm text-gray-500 mt-2">Initializing...</p>
            </div>
        </div>
    </div>

    <!-- Not Configured State -->
    <div id="not-configured" class="hidden text-center py-20">
        <div class="max-w-md mx-auto">
            <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">360Dialog Not Connected</h3>
            <p class="text-gray-600 mb-6">Please configure your 360Dialog connection to access business data.</p>
            <a href="{{ route('settings.360dialog') }}" class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-lg shadow-lg transition-all duration-200 transform hover:scale-105">
                <i class="fas fa-cog mr-2"></i>
                Configure 360Dialog
            </a>
        </div>
    </div>

    <!-- Error State -->
    <div id="error-state" class="hidden text-center py-20">
        <div class="max-w-md mx-auto">
            <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-times text-red-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Error Loading Data</h3>
            <p class="text-gray-600 mb-6" id="error-message">An error occurred while loading business data.</p>
            <button onclick="refreshData()" class="bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white px-6 py-3 rounded-lg shadow-lg transition-all duration-200 transform hover:scale-105">
                <i class="fas fa-redo mr-2"></i>
                Try Again
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div id="main-content" class="hidden">
        <!-- Dashboard Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-blue-100 text-sm font-medium">Total Businesses</p>
                        <p class="text-3xl font-bold" id="total-businesses">0</p>
                    </div>
                    <div class="bg-blue-400 bg-opacity-30 rounded-lg p-3">
                        <i class="fas fa-building text-2xl"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center">
                    <i class="fas fa-arrow-up text-green-300 mr-1"></i>
                    <span class="text-blue-100 text-sm">Active channels</span>
                </div>
            </div>

            <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-green-100 text-sm font-medium">Total Messages</p>
                        <p class="text-3xl font-bold" id="total-messages">0</p>
                    </div>
                    <div class="bg-green-400 bg-opacity-30 rounded-lg p-3">
                        <i class="fas fa-envelope text-2xl"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center">
                    <i class="fas fa-chart-line text-green-300 mr-1"></i>
                    <span class="text-green-100 text-sm">All time</span>
                </div>
            </div>

            <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-purple-100 text-sm font-medium">Total Revenue</p>
                        <p class="text-3xl font-bold" id="total-revenue">$0</p>
                    </div>
                    <div class="bg-purple-400 bg-opacity-30 rounded-lg p-3">
                        <i class="fas fa-dollar-sign text-2xl"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center">
                    <i class="fas fa-coins text-purple-300 mr-1"></i>
                    <span class="text-purple-100 text-sm">Generated</span>
                </div>
            </div>

            <div class="bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-orange-100 text-sm font-medium">Active Status</p>
                        <p class="text-3xl font-bold" id="active-percentage">0%</p>
                    </div>
                    <div class="bg-orange-400 bg-opacity-30 rounded-lg p-3">
                        <i class="fas fa-signal text-2xl"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center">
                    <i class="fas fa-check-circle text-orange-300 mr-1"></i>
                    <span class="text-orange-100 text-sm">Ready channels</span>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div class="flex-1">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text" id="business-search" placeholder="Search businesses by name, phone, or status..." 
                               class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <select id="status-filter" class="border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Status</option>
                        <option value="ready">Ready</option>
                        <option value="pending">Pending</option>
                        <option value="suspended">Suspended</option>
                    </select>
                    <select id="sort-by" class="border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="name">Sort by Name</option>
                        <option value="messages">Sort by Messages</option>
                        <option value="revenue">Sort by Revenue</option>
                        <option value="status">Sort by Status</option>
                    </select>
                    <div class="flex bg-gray-100 rounded-lg p-1">
                        <button id="grid-view-btn" onclick="setViewMode('grid')" class="px-3 py-2 rounded-md text-sm font-medium transition-all duration-200">
                            <i class="fas fa-th-large"></i>
                        </button>
                        <button id="list-view-btn" onclick="setViewMode('list')" class="px-3 py-2 rounded-md text-sm font-medium transition-all duration-200">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Business Cards Container -->
        <div id="businesses-container">
            <!-- Grid View -->
            <div id="grid-view" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <!-- Business cards will be inserted here -->
            </div>

            <!-- List View -->
            <div id="list-view" class="hidden space-y-4">
                <!-- Business list items will be inserted here -->
            </div>
        </div>

        <!-- Pagination -->
        <div class="mt-8 flex items-center justify-between">
            <div class="text-sm text-gray-700" id="pagination-info">
                <!-- Pagination info will be inserted here -->
            </div>
            <div class="flex items-center space-x-2" id="pagination-controls">
                <button id="prev-page" onclick="previousPage()" class="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    <i class="fas fa-chevron-left mr-1"></i> Previous
                </button>
                <span id="page-info" class="px-4 py-2 text-sm text-gray-700">
                    <!-- Page info will be inserted here -->
                </span>
                <button id="next-page" onclick="nextPage()" class="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    Next <i class="fas fa-chevron-right ml-1"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Business Detail Modal -->
    <div id="business-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
            <div id="modal-content">
                <!-- Modal content will be inserted here -->
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let allBusinesses = [];
        let filteredBusinesses = [];
        let currentPage = 0;
        let itemsPerPage = 12;
        let currentViewMode = 'grid';
        let currentSortBy = 'name';
        let isLoading = false;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
        });

        async function initializePage() {
            try {
                showLoadingState();
                await loadBusinessData();
                hideLoadingState();
                showMainContent();
            } catch (error) {
                console.error('Error initializing page:', error);
                showErrorState(error.message);
            }
        }

        function showLoadingState() {
            document.getElementById('loading-state').classList.remove('hidden');
            document.getElementById('main-content').classList.add('hidden');
            document.getElementById('not-configured').classList.add('hidden');
            document.getElementById('error-state').classList.add('hidden');
        }

        function hideLoadingState() {
            document.getElementById('loading-state').classList.add('hidden');
        }

        function showMainContent() {
            document.getElementById('main-content').classList.remove('hidden');
        }

        function showErrorState(message) {
            document.getElementById('error-state').classList.remove('hidden');
            document.getElementById('error-message').textContent = message;
            hideLoadingState();
        }

        function showNotConfiguredState() {
            document.getElementById('not-configured').classList.remove('hidden');
            hideLoadingState();
        }

        async function loadBusinessData() {
            try {
                updateLoadingProgress(10, 'Checking authentication...');

                // Check authentication status
                const authResponse = await fetch('/api/360dialog/auth-status', {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const authData = await authResponse.json();

                if (!authData.authenticated) {
                    showNotConfiguredState();
                    return;
                }

                updateLoadingProgress(30, 'Loading business accounts...');

                // Load all business accounts
                await loadAllBusinessAccounts();

                updateLoadingProgress(70, 'Processing business data...');

                // Process and display data
                processBusinessData();

                updateLoadingProgress(90, 'Finalizing...');

                // Update dashboard stats
                updateDashboardStats();

                updateLoadingProgress(100, 'Complete!');

            } catch (error) {
                console.error('Error loading business data:', error);
                throw error;
            }
        }

        async function loadAllBusinessAccounts() {
            try {
                let offset = 0;
                let limit = 1000;
                let hasMore = true;
                allBusinesses = [];

                while (hasMore) {
                    const response = await fetch(`/api/360dialog/account-details?offset=${offset}&limit=${limit}`, {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    });

                    const data = await response.json();

                    if (data.success) {
                        const channels = data.data.partner_channels || [];

                        // Process channels into business objects
                        for (const channel of channels) {
                            const business = await processChannelToBusiness(channel);
                            allBusinesses.push(business);
                        }

                        const totalCount = data.data.count || 0;

                        if (channels.length < limit || allBusinesses.length >= totalCount) {
                            hasMore = false;
                        } else {
                            offset += limit;
                        }

                        updateLoadingProgress(30 + (allBusinesses.length / totalCount) * 40, `Loaded ${allBusinesses.length} businesses...`);
                    } else {
                        hasMore = false;
                        throw new Error(data.message || 'Failed to load business accounts');
                    }
                }

                filteredBusinesses = [...allBusinesses];
                console.log(`Successfully loaded ${allBusinesses.length} businesses`);
            } catch (error) {
                console.error('Error loading business accounts:', error);
                throw error;
            }
        }

        async function processChannelToBusiness(channel) {
            const business = {
                id: channel.id,
                name: channel.client?.name || 'Unknown Business',
                phone: channel.phone_number || 'N/A',
                status: channel.status || 'unknown',
                channelId: channel.id,
                clientId: channel.client?.id || '',
                projectId: channel.project?.id || '',
                partnerId: channel.partner?.id || '',
                createdAt: channel.client?.created_at || null,
                avatar: generateAvatar(channel.client?.name || 'Unknown'),
                statusColor: getStatusColor(channel.status),
                metrics: {
                    totalMessages: 0,
                    totalRevenue: 0,
                    freeMessages: 0,
                    paidMessages: 0,
                    lastActivity: null
                },
                trafficData: null
            };

            // Try to load traffic data for this business
            try {
                const trafficResponse = await fetch(`/api/360dialog/traffic-data?channel_id=${business.channelId}&client_id=${business.clientId}&project_id=${business.projectId}&partner_id=${business.partnerId}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const trafficData = await trafficResponse.json();

                if (trafficData.success && trafficData.data.usage) {
                    business.trafficData = trafficData.data;

                    // Calculate metrics from traffic data
                    const usage = trafficData.data.usage || [];
                    business.metrics.totalMessages = usage.reduce((sum, period) => sum + (period.quantity || 0), 0);
                    business.metrics.totalRevenue = usage.reduce((sum, period) => sum + (period.total_price || 0), 0);
                    business.metrics.freeMessages = usage.reduce((sum, period) => sum + (period.free_quantity || 0), 0);
                    business.metrics.paidMessages = usage.reduce((sum, period) => sum + (period.paid_quantity || 0), 0);

                    if (usage.length > 0) {
                        business.metrics.lastActivity = usage[0].period_date;
                    }
                }
            } catch (error) {
                console.warn(`Failed to load traffic data for business ${business.name}:`, error);
            }

            return business;
        }

        function generateAvatar(name) {
            const colors = [
                'from-blue-500 to-blue-600',
                'from-green-500 to-green-600',
                'from-purple-500 to-purple-600',
                'from-red-500 to-red-600',
                'from-yellow-500 to-yellow-600',
                'from-indigo-500 to-indigo-600',
                'from-pink-500 to-pink-600',
                'from-teal-500 to-teal-600'
            ];

            const colorIndex = name.length % colors.length;
            return {
                initials: name.charAt(0).toUpperCase(),
                gradient: colors[colorIndex]
            };
        }

        function getStatusColor(status) {
            switch(status) {
                case 'ready': return 'bg-green-100 text-green-800 border-green-200';
                case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
                case 'suspended': return 'bg-red-100 text-red-800 border-red-200';
                default: return 'bg-gray-100 text-gray-800 border-gray-200';
            }
        }

        function updateLoadingProgress(percentage, text) {
            document.getElementById('loading-progress').style.width = percentage + '%';
            document.getElementById('loading-text').textContent = text;
        }

        function processBusinessData() {
            // Sort businesses
            sortBusinesses();

            // Apply filters
            applyFilters();

            // Display businesses
            displayBusinesses();
        }

        function updateDashboardStats() {
            const totalBusinesses = allBusinesses.length;
            const totalMessages = allBusinesses.reduce((sum, business) => sum + business.metrics.totalMessages, 0);
            const totalRevenue = allBusinesses.reduce((sum, business) => sum + business.metrics.totalRevenue, 0);
            const activeBusinesses = allBusinesses.filter(business => business.status === 'ready').length;
            const activePercentage = totalBusinesses > 0 ? Math.round((activeBusinesses / totalBusinesses) * 100) : 0;

            document.getElementById('total-businesses').textContent = totalBusinesses.toLocaleString();
            document.getElementById('total-messages').textContent = totalMessages.toLocaleString();
            document.getElementById('total-revenue').textContent = '$' + totalRevenue.toFixed(2);
            document.getElementById('active-percentage').textContent = activePercentage + '%';
        }

        function sortBusinesses() {
            const sortBy = document.getElementById('sort-by').value;

            filteredBusinesses.sort((a, b) => {
                switch(sortBy) {
                    case 'name':
                        return a.name.localeCompare(b.name);
                    case 'messages':
                        return b.metrics.totalMessages - a.metrics.totalMessages;
                    case 'revenue':
                        return b.metrics.totalRevenue - a.metrics.totalRevenue;
                    case 'status':
                        return a.status.localeCompare(b.status);
                    default:
                        return 0;
                }
            });
        }

        function applyFilters() {
            const searchTerm = document.getElementById('business-search').value.toLowerCase();
            const statusFilter = document.getElementById('status-filter').value;

            filteredBusinesses = allBusinesses.filter(business => {
                const matchesSearch = !searchTerm ||
                    business.name.toLowerCase().includes(searchTerm) ||
                    business.phone.includes(searchTerm) ||
                    business.status.toLowerCase().includes(searchTerm) ||
                    business.channelId.toLowerCase().includes(searchTerm);

                const matchesStatus = !statusFilter || business.status === statusFilter;

                return matchesSearch && matchesStatus;
            });

            sortBusinesses();
            currentPage = 0; // Reset to first page
        }

        function displayBusinesses() {
            const startIndex = currentPage * itemsPerPage;
            const endIndex = Math.min(startIndex + itemsPerPage, filteredBusinesses.length);
            const pageBusinesses = filteredBusinesses.slice(startIndex, endIndex);

            if (currentViewMode === 'grid') {
                displayGridView(pageBusinesses);
            } else {
                displayListView(pageBusinesses);
            }

            updatePaginationControls();
        }

        function displayGridView(businesses) {
            const gridContainer = document.getElementById('grid-view');
            const listContainer = document.getElementById('list-view');

            gridContainer.classList.remove('hidden');
            listContainer.classList.add('hidden');

            if (businesses.length === 0) {
                gridContainer.innerHTML = `
                    <div class="col-span-full text-center py-12">
                        <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-search text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No businesses found</h3>
                        <p class="text-gray-500">Try adjusting your search or filter criteria.</p>
                    </div>
                `;
                return;
            }

            gridContainer.innerHTML = businesses.map(business => `
                <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 cursor-pointer border border-gray-100" onclick="openBusinessModal('${business.id}')">
                    <div class="p-6">
                        <!-- Business Header -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 rounded-full bg-gradient-to-r ${business.avatar.gradient} flex items-center justify-center text-white font-bold text-lg shadow-lg">
                                    ${business.avatar.initials}
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900 text-lg truncate" title="${business.name}">${business.name}</h3>
                                    <p class="text-sm text-gray-500">${business.phone}</p>
                                </div>
                            </div>
                            <span class="px-3 py-1 text-xs font-medium rounded-full border ${business.statusColor}">
                                ${business.status}
                            </span>
                        </div>

                        <!-- Business Metrics -->
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="text-center p-3 bg-blue-50 rounded-lg">
                                <p class="text-2xl font-bold text-blue-600">${business.metrics.totalMessages.toLocaleString()}</p>
                                <p class="text-xs text-blue-500 font-medium">Messages</p>
                            </div>
                            <div class="text-center p-3 bg-green-50 rounded-lg">
                                <p class="text-2xl font-bold text-green-600">$${business.metrics.totalRevenue.toFixed(2)}</p>
                                <p class="text-xs text-green-500 font-medium">Revenue</p>
                            </div>
                        </div>

                        <!-- Quick Stats -->
                        <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                            <span><i class="fas fa-gift text-green-500 mr-1"></i> ${business.metrics.freeMessages} free</span>
                            <span><i class="fas fa-credit-card text-blue-500 mr-1"></i> ${business.metrics.paidMessages} paid</span>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex space-x-2">
                            <button onclick="event.stopPropagation(); viewTrafficData('${business.id}')" class="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200">
                                <i class="fas fa-chart-line mr-1"></i> Traffic
                            </button>
                            <button onclick="event.stopPropagation(); importBusinessData('${business.id}')" class="flex-1 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200">
                                <i class="fas fa-download mr-1"></i> Import
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function displayListView(businesses) {
            const gridContainer = document.getElementById('grid-view');
            const listContainer = document.getElementById('list-view');

            gridContainer.classList.add('hidden');
            listContainer.classList.remove('hidden');

            if (businesses.length === 0) {
                listContainer.innerHTML = `
                    <div class="text-center py-12">
                        <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-search text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No businesses found</h3>
                        <p class="text-gray-500">Try adjusting your search or filter criteria.</p>
                    </div>
                `;
                return;
            }

            listContainer.innerHTML = businesses.map(business => `
                <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer border border-gray-100" onclick="openBusinessModal('${business.id}')">
                    <div class="p-6">
                        <div class="flex items-center justify-between">
                            <!-- Business Info -->
                            <div class="flex items-center space-x-4">
                                <div class="w-16 h-16 rounded-full bg-gradient-to-r ${business.avatar.gradient} flex items-center justify-center text-white font-bold text-xl shadow-lg">
                                    ${business.avatar.initials}
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900 text-xl">${business.name}</h3>
                                    <p class="text-gray-500">${business.phone}</p>
                                    <span class="inline-block px-3 py-1 text-xs font-medium rounded-full border ${business.statusColor} mt-1">
                                        ${business.status}
                                    </span>
                                </div>
                            </div>

                            <!-- Metrics -->
                            <div class="flex items-center space-x-8">
                                <div class="text-center">
                                    <p class="text-2xl font-bold text-blue-600">${business.metrics.totalMessages.toLocaleString()}</p>
                                    <p class="text-sm text-gray-500">Messages</p>
                                </div>
                                <div class="text-center">
                                    <p class="text-2xl font-bold text-green-600">$${business.metrics.totalRevenue.toFixed(2)}</p>
                                    <p class="text-sm text-gray-500">Revenue</p>
                                </div>
                                <div class="text-center">
                                    <p class="text-lg font-semibold text-gray-700">${business.metrics.freeMessages}/${business.metrics.paidMessages}</p>
                                    <p class="text-sm text-gray-500">Free/Paid</p>
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="flex items-center space-x-3">
                                <button onclick="event.stopPropagation(); viewTrafficData('${business.id}')" class="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200">
                                    <i class="fas fa-chart-line mr-2"></i> View Traffic
                                </button>
                                <button onclick="event.stopPropagation(); importBusinessData('${business.id}')" class="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200">
                                    <i class="fas fa-download mr-2"></i> Import Data
                                </button>
                                <button onclick="event.stopPropagation(); openBusinessModal('${business.id}')" class="bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200">
                                    <i class="fas fa-eye mr-2"></i> Details
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function updatePaginationControls() {
            const totalItems = filteredBusinesses.length;
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            const startItem = totalItems > 0 ? (currentPage * itemsPerPage) + 1 : 0;
            const endItem = Math.min((currentPage + 1) * itemsPerPage, totalItems);

            document.getElementById('pagination-info').innerHTML = `Showing ${startItem}-${endItem} of ${totalItems} businesses`;
            document.getElementById('page-info').innerHTML = `Page ${currentPage + 1} of ${totalPages}`;

            document.getElementById('prev-page').disabled = currentPage === 0;
            document.getElementById('next-page').disabled = currentPage >= totalPages - 1;
        }

        function setViewMode(mode) {
            currentViewMode = mode;

            const gridBtn = document.getElementById('grid-view-btn');
            const listBtn = document.getElementById('list-view-btn');

            if (mode === 'grid') {
                gridBtn.classList.add('bg-white', 'text-blue-600', 'shadow-sm');
                gridBtn.classList.remove('text-gray-500');
                listBtn.classList.remove('bg-white', 'text-blue-600', 'shadow-sm');
                listBtn.classList.add('text-gray-500');
            } else {
                listBtn.classList.add('bg-white', 'text-blue-600', 'shadow-sm');
                listBtn.classList.remove('text-gray-500');
                gridBtn.classList.remove('bg-white', 'text-blue-600', 'shadow-sm');
                gridBtn.classList.add('text-gray-500');
            }

            displayBusinesses();
        }

        function previousPage() {
            if (currentPage > 0) {
                currentPage--;
                displayBusinesses();
            }
        }

        function nextPage() {
            const totalPages = Math.ceil(filteredBusinesses.length / itemsPerPage);
            if (currentPage < totalPages - 1) {
                currentPage++;
                displayBusinesses();
            }
        }

        function openBusinessModal(businessId) {
            const business = allBusinesses.find(b => b.id === businessId);
            if (!business) return;

            const modal = document.getElementById('business-modal');
            const modalContent = document.getElementById('modal-content');

            modalContent.innerHTML = `
                <!-- Modal Header -->
                <div class="bg-gradient-to-r ${business.avatar.gradient} px-6 py-4 text-white">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-16 h-16 rounded-full bg-white bg-opacity-20 flex items-center justify-center text-white font-bold text-2xl">
                                ${business.avatar.initials}
                            </div>
                            <div>
                                <h2 class="text-2xl font-bold">${business.name}</h2>
                                <p class="text-blue-100">${business.phone}</p>
                                <span class="inline-block px-3 py-1 text-xs font-medium rounded-full bg-white bg-opacity-20 mt-2">
                                    ${business.status}
                                </span>
                            </div>
                        </div>
                        <button onclick="closeBusinessModal()" class="text-white hover:text-gray-200 transition-colors">
                            <i class="fas fa-times text-2xl"></i>
                        </button>
                    </div>
                </div>

                <!-- Modal Body -->
                <div class="p-6 max-h-[70vh] overflow-y-auto">
                    <!-- Business Overview -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div class="bg-blue-50 rounded-xl p-4 text-center">
                            <div class="text-3xl font-bold text-blue-600">${business.metrics.totalMessages.toLocaleString()}</div>
                            <div class="text-sm text-blue-500 font-medium">Total Messages</div>
                        </div>
                        <div class="bg-green-50 rounded-xl p-4 text-center">
                            <div class="text-3xl font-bold text-green-600">$${business.metrics.totalRevenue.toFixed(2)}</div>
                            <div class="text-sm text-green-500 font-medium">Total Revenue</div>
                        </div>
                        <div class="bg-purple-50 rounded-xl p-4 text-center">
                            <div class="text-3xl font-bold text-purple-600">${business.metrics.freeMessages.toLocaleString()}</div>
                            <div class="text-sm text-purple-500 font-medium">Free Messages</div>
                        </div>
                        <div class="bg-orange-50 rounded-xl p-4 text-center">
                            <div class="text-3xl font-bold text-orange-600">${business.metrics.paidMessages.toLocaleString()}</div>
                            <div class="text-sm text-orange-500 font-medium">Paid Messages</div>
                        </div>
                    </div>

                    <!-- Business Details -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <div class="bg-gray-50 rounded-xl p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Business Information</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Channel ID:</span>
                                    <span class="font-mono text-sm">${business.channelId}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Client ID:</span>
                                    <span class="font-mono text-sm">${business.clientId}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Project ID:</span>
                                    <span class="font-mono text-sm">${business.projectId}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Partner ID:</span>
                                    <span class="font-mono text-sm">${business.partnerId}</span>
                                </div>
                                ${business.createdAt ? `
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Created:</span>
                                    <span>${new Date(business.createdAt).toLocaleDateString()}</span>
                                </div>
                                ` : ''}
                                ${business.metrics.lastActivity ? `
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Last Activity:</span>
                                    <span>${new Date(business.metrics.lastActivity).toLocaleDateString()}</span>
                                </div>
                                ` : ''}
                            </div>
                        </div>

                        <div class="bg-gray-50 rounded-xl p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                            <div class="space-y-3">
                                <button onclick="importBusinessData('${business.id}')" class="w-full bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white px-4 py-3 rounded-lg font-medium transition-all duration-200">
                                    <i class="fas fa-download mr-2"></i> Import to Local Database
                                </button>
                                <button onclick="exportBusinessData('${business.id}')" class="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-4 py-3 rounded-lg font-medium transition-all duration-200">
                                    <i class="fas fa-file-export mr-2"></i> Export Traffic Data
                                </button>
                                <button onclick="viewFullTrafficData('${business.id}')" class="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-4 py-3 rounded-lg font-medium transition-all duration-200">
                                    <i class="fas fa-chart-line mr-2"></i> View Full Traffic Analysis
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Traffic Data Preview -->
                    ${business.trafficData ? generateTrafficPreview(business.trafficData) : '<div class="text-center py-8 text-gray-500">No traffic data available</div>'}
                </div>
            `;

            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeBusinessModal() {
            document.getElementById('business-modal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function generateTrafficPreview(trafficData) {
            const usage = trafficData.usage || [];
            const recentUsage = usage.slice(0, 5);

            return `
                <div class="bg-gray-50 rounded-xl p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Traffic Data</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full">
                            <thead>
                                <tr class="text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <th class="pb-2">Period</th>
                                    <th class="pb-2">Messages</th>
                                    <th class="pb-2">Free</th>
                                    <th class="pb-2">Paid</th>
                                    <th class="pb-2">Revenue</th>
                                </tr>
                            </thead>
                            <tbody class="text-sm">
                                ${recentUsage.map(period => `
                                    <tr class="border-t border-gray-200">
                                        <td class="py-2 font-medium">${new Date(period.period_date).toLocaleDateString()}</td>
                                        <td class="py-2">${(period.quantity || 0).toLocaleString()}</td>
                                        <td class="py-2 text-green-600">${(period.free_quantity || 0).toLocaleString()}</td>
                                        <td class="py-2 text-blue-600">${(period.paid_quantity || 0).toLocaleString()}</td>
                                        <td class="py-2 font-semibold">$${(period.total_price || 0).toFixed(2)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                    ${usage.length > 5 ? `<p class="text-sm text-gray-500 mt-4">Showing 5 of ${usage.length} periods</p>` : ''}
                </div>
            `;
        }

        // Event Listeners
        document.getElementById('business-search').addEventListener('input', applyFilters);
        document.getElementById('status-filter').addEventListener('change', applyFilters);
        document.getElementById('sort-by').addEventListener('change', () => {
            processBusinessData();
            displayBusinesses();
        });

        // Initialize grid view as default
        setViewMode('grid');

        // Business Action Functions
        async function importBusinessData(businessId) {
            const business = allBusinesses.find(b => b.id === businessId);
            if (!business || !business.trafficData) {
                alert('No traffic data available for this business');
                return;
            }

            try {
                const response = await fetch('/api/360dialog/import-traffic-data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        channel_id: business.channelId,
                        traffic_data: business.trafficData
                    })
                });

                const result = await response.json();

                if (result.success) {
                    alert(`Successfully imported ${result.imported_records} traffic records for ${business.name}`);
                } else {
                    alert(`Import failed: ${result.message}`);
                }
            } catch (error) {
                console.error('Import error:', error);
                alert('Failed to import business data');
            }
        }

        async function importAllBusinesses() {
            if (isLoading) return;

            isLoading = true;
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Importing...';
            button.disabled = true;

            try {
                let importedCount = 0;
                let skippedCount = 0;

                for (const business of allBusinesses) {
                    if (business.trafficData) {
                        try {
                            const response = await fetch('/api/360dialog/import-traffic-data', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-Requested-With': 'XMLHttpRequest',
                                    'Accept': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                                },
                                body: JSON.stringify({
                                    channel_id: business.channelId,
                                    traffic_data: business.trafficData
                                })
                            });

                            const result = await response.json();

                            if (result.success) {
                                importedCount += result.imported_records;
                                skippedCount += result.skipped_records;
                            }
                        } catch (error) {
                            console.error(`Failed to import data for ${business.name}:`, error);
                        }
                    }
                }

                alert(`Import completed! Imported ${importedCount} records, skipped ${skippedCount} duplicates.`);
            } catch (error) {
                console.error('Bulk import error:', error);
                alert('Failed to import all business data');
            } finally {
                isLoading = false;
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }

        function exportBusinessData(businessId) {
            const business = allBusinesses.find(b => b.id === businessId);
            if (!business || !business.trafficData) {
                alert('No traffic data available for this business');
                return;
            }

            const usage = business.trafficData.usage || [];
            if (usage.length === 0) {
                alert('No usage data available to export');
                return;
            }

            // Create CSV content
            const headers = [
                'Business Name', 'Phone Number', 'Period Date', 'Total Quantity', 'Free Quantity', 'Paid Quantity',
                'Business Initiated Qty', 'User Initiated Qty', 'Authentication Qty', 'Marketing Qty', 'Service Qty', 'Utility Qty',
                'Total Price'
            ];

            const csvContent = [
                headers.join(','),
                ...usage.map(period => [
                    business.name,
                    business.phone,
                    period.period_date,
                    period.quantity || 0,
                    period.free_quantity || 0,
                    period.paid_quantity || 0,
                    period.business_initiated_quantity || 0,
                    period.user_initiated_quantity || 0,
                    period.authentication_quantity || 0,
                    period.marketing_quantity || 0,
                    period.service_quantity || 0,
                    period.utility_quantity || 0,
                    period.total_price || 0
                ].join(','))
            ].join('\n');

            // Download CSV file
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `${business.name.replace(/[^a-z0-9]/gi, '_')}_traffic_data_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function viewTrafficData(businessId) {
            const business = allBusinesses.find(b => b.id === businessId);
            if (business) {
                // Store selected business for traffic page
                localStorage.setItem('selectedChannel', JSON.stringify({
                    id: business.channelId,
                    client_id: business.clientId,
                    project_id: business.projectId,
                    partner_id: business.partnerId,
                    client_name: business.name,
                    phone_number: business.phone,
                    status: business.status
                }));

                // Navigate to traffic page
                window.location.href = '/360dialog/traffic';
            }
        }

        function viewFullTrafficData(businessId) {
            viewTrafficData(businessId);
        }

        function refreshData() {
            location.reload();
        }

        // Close modal when clicking outside
        document.getElementById('business-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeBusinessModal();
            }
        });
    </script>
</x-admin-layout>
