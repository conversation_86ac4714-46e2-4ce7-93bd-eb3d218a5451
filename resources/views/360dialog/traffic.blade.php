<x-admin-layout>
    <x-slot name="header">
        <h1 class="text-2xl font-bold text-gray-900">WhatsApp Traffic</h1>
        <p class="text-gray-600">Monitor and analyze WhatsApp message traffic</p>
    </x-slot>

    <div id="loading-state" class="bg-white rounded-lg shadow p-8 text-center">
        <i class="fas fa-spinner fa-spin text-4xl text-gray-400 mb-4"></i>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Loading Traffic Data</h3>
        <p class="text-gray-600">Checking 360Dialog configuration...</p>
    </div>

    <div id="not-configured" class="bg-white rounded-lg shadow p-8 text-center hidden">
        <i class="fas fa-chart-line text-6xl text-gray-400 mb-4"></i>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Traffic Analytics</h3>
        <p class="text-gray-600 mb-6">View detailed analytics of your WhatsApp message traffic and engagement metrics.</p>
        <a href="{{ route('settings.360dialog') }}" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium">
            Configure Integration
        </a>
    </div>

    <div id="configured" class="hidden">
        <!-- Channel Selection Section -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Select Business Channel</h3>
                <div class="flex items-center space-x-2">
                    <button onclick="toggleViewMode()" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm">
                        <i class="fas fa-th-large mr-1" id="view-mode-icon"></i> <span id="view-mode-text">Grid View</span>
                    </button>
                    <button onclick="refreshChannelList()" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                        <i class="fas fa-refresh mr-1"></i> Refresh
                    </button>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="mb-4 flex items-center space-x-4">
                <div class="flex-1">
                    <input type="text" id="channel-search" placeholder="Search by business name, phone number, or status..."
                           class="w-full border-gray-300 rounded-md shadow-sm" onkeyup="filterChannels()">
                </div>
                <div>
                    <select id="status-filter" class="border-gray-300 rounded-md shadow-sm" onchange="filterChannels()">
                        <option value="">All Status</option>
                        <option value="ready">Ready</option>
                        <option value="pending">Pending</option>
                        <option value="suspended">Suspended</option>
                    </select>
                </div>
            </div>

            <!-- List View (Default) -->
            <div id="list-view" class="space-y-3">
                <div id="channels-list">
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                        <p>Loading channels...</p>
                    </div>
                </div>
            </div>

            <!-- Grid View -->
            <div id="grid-view" class="hidden">
                <div id="channels-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="text-center py-8 text-gray-500 col-span-full">
                        <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                        <p>Loading channels...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Traffic Overview Section -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-dollar-sign text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Current Balance</p>
                        <p class="text-2xl font-semibold text-gray-900" id="current-balance">Loading...</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-chart-line text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Template Cost</p>
                        <p class="text-2xl font-semibold text-gray-900" id="template-cost">Loading...</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fas fa-calendar text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Granularity</p>
                        <p class="text-2xl font-semibold text-gray-900" id="granularity">Loading...</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                        <i class="fas fa-envelope text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Messages</p>
                        <p class="text-2xl font-semibold text-gray-900" id="total-messages">Loading...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage Details Section -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Usage Details</h3>
            <div id="usage-content" class="space-y-4">
                <!-- Usage data will be loaded here -->
            </div>
        </div>
    </div>

    <div id="error-state" class="bg-white rounded-lg shadow p-8 text-center hidden">
        <i class="fas fa-exclamation-triangle text-6xl text-red-500 mb-4"></i>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Connection Error</h3>
        <p class="text-gray-600 mb-6" id="error-message">Unable to connect to 360Dialog API.</p>
        <button onclick="loadTrafficData()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium mr-4">
            Retry
        </button>
        <a href="{{ route('settings.360dialog') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium">
            Check Configuration
        </a>
    </div>

    <script>
        let availableChannels = [];
        let filteredChannels = [];
        let selectedChannel = null;
        let isGridView = false;

        async function loadTrafficData() {
            try {
                // Show loading state
                document.getElementById('loading-state').classList.remove('hidden');
                document.getElementById('not-configured').classList.add('hidden');
                document.getElementById('configured').classList.add('hidden');
                document.getElementById('error-state').classList.add('hidden');

                // Check authentication status first
                const authResponse = await fetch('/api/360dialog/auth/status', {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const authData = await authResponse.json();

                if (!authData.authenticated) {
                    // Not configured
                    document.getElementById('loading-state').classList.add('hidden');
                    document.getElementById('not-configured').classList.remove('hidden');
                    return;
                }

                // Load available channels first
                await loadAvailableChannels();

                // Check if there's a pre-selected channel from localStorage
                const storedChannel = localStorage.getItem('selectedChannel');
                if (storedChannel) {
                    try {
                        selectedChannel = JSON.parse(storedChannel);
                        localStorage.removeItem('selectedChannel'); // Clear after use
                    } catch (e) {
                        console.warn('Failed to parse stored channel:', e);
                    }
                }

                // Show configured state
                document.getElementById('loading-state').classList.add('hidden');
                document.getElementById('configured').classList.remove('hidden');

                // Load traffic data for selected channel if available
                if (selectedChannel && selectedChannel.id) {
                    selectChannelById(selectedChannel.id);
                    await loadTrafficForSelectedChannel();
                }

            } catch (error) {
                console.error('Error loading traffic data:', error);
                document.getElementById('loading-state').classList.add('hidden');
                document.getElementById('error-state').classList.remove('hidden');
                document.getElementById('error-message').textContent = error.message || 'Failed to load traffic data. Please try again.';
            }
        }

        async function loadAvailableChannels() {
            try {
                const response = await fetch('/api/360dialog/available-channels', {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    availableChannels = data.channels;
                    filteredChannels = [...availableChannels];
                    displayChannels();
                } else {
                    console.error('Failed to load channels:', data.message);
                    showChannelError('Failed to load channels');
                }
            } catch (error) {
                console.error('Error loading channels:', error);
                showChannelError('Error loading channels');
            }
        }

        function showChannelError(message) {
            const listView = document.getElementById('channels-list');
            const gridView = document.getElementById('channels-grid');

            const errorHtml = `
                <div class="text-center py-8 text-red-500">
                    <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                    <p>${message}</p>
                </div>
            `;

            listView.innerHTML = errorHtml;
            gridView.innerHTML = errorHtml;
        }

        function displayChannels() {
            if (filteredChannels.length === 0) {
                const noResultsHtml = `
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-search text-2xl mb-2"></i>
                        <p>No channels found matching your criteria</p>
                    </div>
                `;
                document.getElementById('channels-list').innerHTML = noResultsHtml;
                document.getElementById('channels-grid').innerHTML = noResultsHtml;
                return;
            }

            displayListView();
            displayGridView();
        }

        function displayListView() {
            const listContainer = document.getElementById('channels-list');

            const listHtml = filteredChannels.map(channel => `
                <div class="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors ${selectedChannel && selectedChannel.id === channel.channel_id ? 'ring-2 ring-blue-500 bg-blue-50' : ''}"
                     onclick="selectChannel('${channel.channel_id}')">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold text-lg">
                                    ${channel.client_name.charAt(0).toUpperCase()}
                                </div>
                            </div>
                            <div>
                                <h4 class="text-lg font-semibold text-gray-900">${channel.client_name}</h4>
                                <p class="text-sm text-gray-600">${channel.phone_number}</p>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="px-2 py-1 text-xs rounded-full ${getStatusColor(channel.status)}">${channel.status}</span>
                                    <span class="text-xs text-gray-500">ID: ${channel.channel_id}</span>
                                </div>
                            </div>
                        </div>
                        <div class="text-right">
                            <button class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                                <i class="fas fa-chart-line mr-1"></i> View Traffic
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');

            listContainer.innerHTML = listHtml;
        }

        function displayGridView() {
            const gridContainer = document.getElementById('channels-grid');

            const gridHtml = filteredChannels.map(channel => `
                <div class="border rounded-lg p-4 hover:shadow-lg cursor-pointer transition-all ${selectedChannel && selectedChannel.id === channel.channel_id ? 'ring-2 ring-blue-500 bg-blue-50' : ''}"
                     onclick="selectChannel('${channel.channel_id}')">
                    <div class="text-center">
                        <div class="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold text-xl mx-auto mb-3">
                            ${channel.client_name.charAt(0).toUpperCase()}
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-1">${channel.client_name}</h4>
                        <p class="text-sm text-gray-600 mb-2">${channel.phone_number}</p>
                        <span class="px-2 py-1 text-xs rounded-full ${getStatusColor(channel.status)} mb-3 inline-block">${channel.status}</span>
                        <div class="text-xs text-gray-500 mb-3">ID: ${channel.channel_id}</div>
                        <button class="w-full bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm">
                            <i class="fas fa-chart-line mr-1"></i> View Traffic
                        </button>
                    </div>
                </div>
            `).join('');

            gridContainer.innerHTML = gridHtml;
        }

        function getStatusColor(status) {
            switch(status) {
                case 'ready': return 'bg-green-100 text-green-800';
                case 'pending': return 'bg-yellow-100 text-yellow-800';
                case 'suspended': return 'bg-red-100 text-red-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function selectChannel(channelId) {
            const channel = availableChannels.find(c => c.channel_id === channelId);
            if (channel) {
                selectedChannel = {
                    id: channelId,
                    client_id: channel.client_id,
                    project_id: channel.project_id,
                    partner_id: channel.partner_id,
                    client_name: channel.client_name,
                    phone_number: channel.phone_number,
                    status: channel.status
                };

                // Update visual selection
                displayChannels();

                // Load traffic data
                loadTrafficForSelectedChannel();
            }
        }

        function toggleViewMode() {
            isGridView = !isGridView;

            const listView = document.getElementById('list-view');
            const gridView = document.getElementById('grid-view');
            const icon = document.getElementById('view-mode-icon');
            const text = document.getElementById('view-mode-text');

            if (isGridView) {
                listView.classList.add('hidden');
                gridView.classList.remove('hidden');
                icon.className = 'fas fa-list mr-1';
                text.textContent = 'List View';
            } else {
                listView.classList.remove('hidden');
                gridView.classList.add('hidden');
                icon.className = 'fas fa-th-large mr-1';
                text.textContent = 'Grid View';
            }
        }

        function filterChannels() {
            const searchTerm = document.getElementById('channel-search').value.toLowerCase();
            const statusFilter = document.getElementById('status-filter').value;

            filteredChannels = availableChannels.filter(channel => {
                const matchesSearch = !searchTerm ||
                    channel.client_name.toLowerCase().includes(searchTerm) ||
                    channel.phone_number.includes(searchTerm) ||
                    channel.status.toLowerCase().includes(searchTerm) ||
                    channel.channel_id.toLowerCase().includes(searchTerm);

                const matchesStatus = !statusFilter || channel.status === statusFilter;

                return matchesSearch && matchesStatus;
            });

            displayChannels();
        }

        function selectChannelById(channelId) {
            selectChannel(channelId);
        }

        async function loadTrafficForSelectedChannel() {
            if (!selectedChannel) {
                clearTrafficData();
                return;
            }

            try {
                const response = await fetch(`/api/360dialog/traffic-data?channel_id=${selectedChannel.id}&client_id=${selectedChannel.client_id}&project_id=${selectedChannel.project_id}&partner_id=${selectedChannel.partner_id}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const trafficData = await response.json();

                if (trafficData.success) {
                    displayTrafficData(trafficData.data);
                } else {
                    throw new Error(trafficData.message || 'Failed to load traffic data');
                }
            } catch (error) {
                console.error('Error loading traffic data:', error);
                clearTrafficData();
                alert('Failed to load traffic data for selected channel. Please try again.');
            }
        }

        function clearTrafficData() {
            document.getElementById('current-balance').textContent = 'No data';
            document.getElementById('template-cost').textContent = 'No data';
            document.getElementById('granularity').textContent = 'No data';
            document.getElementById('total-messages').textContent = 'No data';
            document.getElementById('usage-content').innerHTML = '<p class="text-gray-600">Select a channel to view traffic data.</p>';
        }

        function refreshChannelList() {
            loadAvailableChannels();
        }

        function exportTrafficData() {
            if (!selectedChannel || !currentTrafficData) {
                alert('No traffic data available to export');
                return;
            }

            const usage = currentTrafficData.usage || [];
            if (usage.length === 0) {
                alert('No usage data available to export');
                return;
            }

            // Create CSV content
            const headers = [
                'Period Date', 'Total Quantity', 'Free Quantity', 'Paid Quantity',
                'Business Initiated Qty', 'Business Initiated Paid', 'Business Initiated Price',
                'User Initiated Qty', 'User Initiated Paid', 'User Initiated Price',
                'Authentication Qty', 'Authentication Paid', 'Authentication Price',
                'Marketing Qty', 'Marketing Paid', 'Marketing Price',
                'Service Qty', 'Service Paid', 'Service Price',
                'Utility Qty', 'Utility Paid', 'Utility Price',
                'Total Price', 'Free Tier', 'Free Entry Point'
            ];

            const csvContent = [
                headers.join(','),
                ...usage.map(period => [
                    period.period_date,
                    period.quantity || 0,
                    period.free_quantity || 0,
                    period.paid_quantity || 0,
                    period.business_initiated_quantity || 0,
                    period.business_initiated_paid_quantity || 0,
                    period.business_initiated_price || 0,
                    period.user_initiated_quantity || 0,
                    period.user_initiated_paid_quantity || 0,
                    period.user_initiated_price || 0,
                    period.authentication_quantity || 0,
                    period.authentication_paid_quantity || 0,
                    period.authentication_price || 0,
                    period.marketing_quantity || 0,
                    period.marketing_paid_quantity || 0,
                    period.marketing_price || 0,
                    period.service_quantity || 0,
                    period.service_paid_quantity || 0,
                    period.service_price || 0,
                    period.utility_quantity || 0,
                    period.utility_paid_quantity || 0,
                    period.utility_price || 0,
                    period.total_price || 0,
                    period.free_tier || 0,
                    period.free_entry_point || 0
                ].join(','))
            ].join('\n');

            // Download CSV file
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `traffic_data_${selectedChannel.id}_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        async function importToLocalDB() {
            if (!selectedChannel || !currentTrafficData) {
                alert('No traffic data available to import');
                return;
            }

            try {
                const response = await fetch('/api/360dialog/import-traffic-data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        channel_id: selectedChannel.id,
                        traffic_data: currentTrafficData
                    })
                });

                const result = await response.json();

                if (result.success) {
                    alert(`Successfully imported ${result.imported_records} traffic records to local database`);
                } else {
                    alert(`Import failed: ${result.message}`);
                }
            } catch (error) {
                console.error('Import error:', error);
                alert('Failed to import data to local database');
            }
        }

        let currentTrafficData = null;

        function displayTrafficData(data) {
            currentTrafficData = data;
            // Update overview cards
            document.getElementById('current-balance').textContent = `${data.balance || 0} ${(data.currency || 'USD').toUpperCase()}`;
            document.getElementById('template-cost').textContent = `${data.estimated_template_cost || 0} ${(data.currency || 'USD').toUpperCase()}`;
            document.getElementById('granularity').textContent = (data.granularity || 'month').charAt(0).toUpperCase() + (data.granularity || 'month').slice(1);

            // Calculate total messages from usage data
            const usage = data.usage || [];
            const totalMessages = usage.reduce((sum, period) => sum + (period.quantity || 0), 0);
            document.getElementById('total-messages').textContent = totalMessages.toLocaleString();

            // Display usage data
            const usageContent = document.getElementById('usage-content');

            if (usage.length === 0) {
                usageContent.innerHTML = '<p class="text-gray-600">No usage data available.</p>';
                return;
            }

            usageContent.innerHTML = `
                <div class="mb-4 flex justify-between items-center">
                    <h4 class="text-lg font-semibold text-gray-900">Traffic History (${usage.length} periods)</h4>
                    <div class="flex items-center space-x-2">
                        <button onclick="exportTrafficData()" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                            <i class="fas fa-download mr-1"></i> Export CSV
                        </button>
                        <button onclick="importToLocalDB()" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                            <i class="fas fa-database mr-1"></i> Import to DB
                        </button>
                    </div>
                </div>
                <div class="overflow-x-auto max-h-96 overflow-y-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0">
                            <tr>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Free</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Paid</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business Init.</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User Init.</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Authentication</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Marketing</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Utility</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Price</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${usage.map(period => `
                                <tr class="hover:bg-gray-50">
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                                        ${new Date(period.period_date).toLocaleDateString()}
                                    </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <span class="font-semibold">${period.quantity || 0}</span>
                                    </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-green-600">
                                        ${period.free_quantity || 0}
                                        ${period.free_tier ? `<br><span class="text-xs text-gray-500">(Tier: ${period.free_tier})</span>` : ''}
                                    </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-red-600">
                                        ${period.paid_quantity || 0}
                                    </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <div>${period.business_initiated_quantity || 0}</div>
                                        <div class="text-xs text-gray-500">Paid: ${period.business_initiated_paid_quantity || 0}</div>
                                        <div class="text-xs text-green-600">$${(period.business_initiated_price || 0).toFixed(3)}</div>
                                    </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <div>${period.user_initiated_quantity || 0}</div>
                                        <div class="text-xs text-gray-500">Paid: ${period.user_initiated_paid_quantity || 0}</div>
                                        <div class="text-xs text-green-600">$${(period.user_initiated_price || 0).toFixed(3)}</div>
                                    </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <div>${period.authentication_quantity || 0}</div>
                                        <div class="text-xs text-gray-500">Paid: ${period.authentication_paid_quantity || 0}</div>
                                        <div class="text-xs text-green-600">$${(period.authentication_price || 0).toFixed(3)}</div>
                                    </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <div>${period.marketing_quantity || 0}</div>
                                        <div class="text-xs text-gray-500">Paid: ${period.marketing_paid_quantity || 0}</div>
                                        <div class="text-xs text-green-600">$${(period.marketing_price || 0).toFixed(3)}</div>
                                    </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <div>${period.service_quantity || 0}</div>
                                        <div class="text-xs text-gray-500">Paid: ${period.service_paid_quantity || 0}</div>
                                        <div class="text-xs text-green-600">$${(period.service_price || 0).toFixed(3)}</div>
                                    </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <div>${period.utility_quantity || 0}</div>
                                        <div class="text-xs text-gray-500">Paid: ${period.utility_paid_quantity || 0}</div>
                                        <div class="text-xs text-green-600">$${(period.utility_price || 0).toFixed(3)}</div>
                                    </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">
                                        $${(period.total_price || 0).toFixed(3)}
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>

                <!-- Summary Statistics -->
                <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-blue-900 mb-2">Message Type Breakdown</h4>
                        <div class="space-y-1 text-sm">
                            <div class="flex justify-between">
                                <span class="text-blue-700">Business Initiated:</span>
                                <span class="font-medium">${usage.reduce((sum, p) => sum + (p.business_initiated_quantity || 0), 0)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-blue-700">User Initiated:</span>
                                <span class="font-medium">${usage.reduce((sum, p) => sum + (p.user_initiated_quantity || 0), 0)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-blue-700">Authentication:</span>
                                <span class="font-medium">${usage.reduce((sum, p) => sum + (p.authentication_quantity || 0), 0)}</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-green-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-green-900 mb-2">Cost Breakdown</h4>
                        <div class="space-y-1 text-sm">
                            <div class="flex justify-between">
                                <span class="text-green-700">Total Cost:</span>
                                <span class="font-medium">$${usage.reduce((sum, p) => sum + (p.total_price || 0), 0).toFixed(3)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-green-700">Free Messages:</span>
                                <span class="font-medium">${usage.reduce((sum, p) => sum + (p.free_quantity || 0), 0)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-green-700">Paid Messages:</span>
                                <span class="font-medium">${usage.reduce((sum, p) => sum + (p.paid_quantity || 0), 0)}</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-purple-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-purple-900 mb-2">Service Types</h4>
                        <div class="space-y-1 text-sm">
                            <div class="flex justify-between">
                                <span class="text-purple-700">Marketing:</span>
                                <span class="font-medium">${usage.reduce((sum, p) => sum + (p.marketing_quantity || 0), 0)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-purple-700">Service:</span>
                                <span class="font-medium">${usage.reduce((sum, p) => sum + (p.service_quantity || 0), 0)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-purple-700">Utility:</span>
                                <span class="font-medium">${usage.reduce((sum, p) => sum + (p.utility_quantity || 0), 0)}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Load data when page loads
        document.addEventListener('DOMContentLoaded', loadTrafficData);
    </script>
</x-admin-layout>
