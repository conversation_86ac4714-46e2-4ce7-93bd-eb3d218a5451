<?php if (isset($component)) { $__componentOriginale0f1cdd055772eb1d4a99981c240763e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale0f1cdd055772eb1d4a99981c240763e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h1 class="text-2xl font-bold text-gray-900">360Dialog Settings</h1>
        <p class="text-gray-600">Configure your WhatsApp Business API integration</p>
     <?php $__env->endSlot(); ?>

    <!-- Authentication Status -->
    <div id="auth-status" class="mb-6">
        <!-- Will be populated by JavaScript -->
    </div>

    <!-- Bearer Token Configuration -->
    <div class="bg-white rounded-lg shadow p-6">
        <!-- Token Input Section -->
        <div id="token-input-section">
            <div class="space-y-6">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fab fa-whatsapp text-green-600 mr-2"></i>
                        Bearer Token Authentication
                    </h3>
                    <p class="text-gray-600 mb-4">Enter your 360Dialog Bearer token to authenticate with the WhatsApp Business API.</p>

                    <div class="max-w-2xl">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Bearer Token</label>
                        <div class="relative">
                            <input type="password" id="bearer_token"
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 pr-12 focus:outline-none focus:ring-2 focus:ring-green-500"
                                   placeholder="Enter your 360Dialog Bearer token"
                                   minlength="20"
                                   maxlength="5000">
                            <button type="button" id="toggle-token-visibility"
                                    class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <i class="fas fa-eye text-gray-400 hover:text-gray-600"></i>
                            </button>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">
                            Enter your 360Dialog JWT Bearer token (typically 1328 characters). This should be a JWT token with RS256 algorithm from your 360Dialog dashboard.
                        </p>
                        <div class="text-xs text-blue-600 mt-2 p-2 bg-blue-50 rounded border">
                            <strong>Example JWT Token Format:</strong><br>
                            <code class="text-xs break-all">********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************</code>
                        </div>
                        <div id="token-error" class="text-red-500 text-sm mt-1 hidden"></div>
                    </div>

                    <div class="mt-4 flex space-x-3">
                        <button type="button" id="save-token-btn"
                                class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-save mr-2"></i>
                            Save & Validate Token
                        </button>

                        <button type="button" id="test-connection-btn"
                                class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-plug mr-2"></i>
                            Test Connection
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Connected State Section -->
        <div id="connected-section" class="space-y-6 hidden">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-check-circle text-green-600 mr-2"></i>
                    Connected to 360Dialog
                </h3>
                <p class="text-gray-600 mb-4">Your WhatsApp Business API integration is active and ready to use.</p>

                <div id="connection-details" class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                    <!-- Will be populated with connection details -->
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <div class="bg-white border border-gray-200 rounded-lg p-4 text-center">
                        <i class="fas fa-user-friends text-2xl text-blue-600 mb-2"></i>
                        <h4 class="font-medium text-gray-900">Account Info</h4>
                        <button type="button" id="get-account-info-btn"
                                class="text-sm text-blue-600 hover:text-blue-800 mt-1">
                            View Details
                        </button>
                    </div>

                    <div class="bg-white border border-gray-200 rounded-lg p-4 text-center">
                        <i class="fas fa-template text-2xl text-purple-600 mb-2"></i>
                        <h4 class="font-medium text-gray-900">Templates</h4>
                        <button type="button" id="sync-templates-btn"
                                class="text-sm text-purple-600 hover:text-purple-800 mt-1">
                            Sync Templates
                        </button>
                    </div>

                    <div class="bg-white border border-gray-200 rounded-lg p-4 text-center">
                        <i class="fas fa-chart-line text-2xl text-orange-600 mb-2"></i>
                        <h4 class="font-medium text-gray-900">Traffic Data</h4>
                        <button type="button" id="sync-traffic-btn"
                                class="text-sm text-orange-600 hover:text-orange-800 mt-1">
                            Sync Data
                        </button>
                    </div>

                    <div class="bg-white border border-gray-200 rounded-lg p-4 text-center">
                        <i class="fas fa-paper-plane text-2xl text-green-600 mb-2"></i>
                        <h4 class="font-medium text-gray-900">Test Message</h4>
                        <button type="button" id="send-test-message-btn"
                                class="text-sm text-green-600 hover:text-green-800 mt-1">
                            Send Test
                        </button>
                    </div>
                </div>

                <div class="flex space-x-3">
                    <button type="button" id="update-token-btn"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium">
                        <i class="fas fa-edit mr-2"></i>
                        Update Token
                    </button>

                    <button type="button" id="disconnect-btn"
                            class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium">
                        <i class="fas fa-unlink mr-2"></i>
                        Disconnect
                    </button>
                </div>
            </div>
        </div>

    </div>

    <!-- Test Message Modal -->
    <div id="test-message-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Send Test Message</h3>

            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Recipient Phone Number</label>
                    <input type="tel" id="test_phone"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                           placeholder="+966501234567">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                    <textarea id="test_message" rows="3"
                              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                              placeholder="Enter your test message">Hello! This is a test message from 360Dialog integration.</textarea>
                </div>

                <div id="test-message-error" class="text-red-500 text-sm hidden"></div>
            </div>

            <div class="mt-6 flex space-x-3">
                <button type="button" id="send-test-btn"
                        class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium">
                    <i class="fas fa-paper-plane mr-2"></i>
                    Send Message
                </button>

                <button type="button" id="cancel-test-btn"
                        class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium">
                    Cancel
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
            <div class="flex items-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mr-3"></div>
                <span id="loading-text">Processing...</span>
            </div>
        </div>
    </div>

    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthStatus();
            setupEventListeners();
        });

        // Setup event listeners
        function setupEventListeners() {
            document.getElementById('save-token-btn').addEventListener('click', saveToken);
            document.getElementById('test-connection-btn').addEventListener('click', testConnection);
            document.getElementById('update-token-btn').addEventListener('click', showTokenInput);
            document.getElementById('disconnect-btn').addEventListener('click', disconnect);
            document.getElementById('toggle-token-visibility').addEventListener('click', toggleTokenVisibility);

            // Connected section buttons
            document.getElementById('get-account-info-btn').addEventListener('click', getAccountInfo);
            document.getElementById('sync-templates-btn').addEventListener('click', syncTemplates);
            document.getElementById('sync-traffic-btn').addEventListener('click', syncTraffic);
            document.getElementById('send-test-message-btn').addEventListener('click', showTestMessageModal);

            // Test message modal
            document.getElementById('send-test-btn').addEventListener('click', sendTestMessage);
            document.getElementById('cancel-test-btn').addEventListener('click', hideTestMessageModal);

            // Auto-format phone number in test modal
            document.getElementById('test_phone').addEventListener('input', formatPhoneNumber);

            // Enter key handling
            document.getElementById('bearer_token').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    saveToken();
                }
            });
        }

        // Check authentication status
        async function checkAuthStatus() {
            try {
                const response = await fetch('/api/360dialog/auth/status');
                const data = await response.json();

                if (data.authenticated) {
                    showConnectedState(data);
                } else {
                    showTokenInputState();
                    updateAuthStatus('not_authenticated', data.message || 'Not connected to 360Dialog');
                }
            } catch (error) {
                console.error('Error checking auth status:', error);
                showTokenInputState();
                updateAuthStatus('error', 'Error checking connection status');
            }
        }

        // Save and validate Bearer token
        async function saveToken() {
            const bearerToken = document.getElementById('bearer_token').value.trim();

            if (!validateToken(bearerToken)) {
                return;
            }

            showLoading('Validating Bearer token...');

            try {
                const response = await fetch('/api/360dialog/auth/update-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ bearer_token: bearerToken })
                });

                const data = await response.json();
                hideLoading();

                if (data.success) {
                    showSuccess('Bearer token saved and validated successfully!');
                    checkAuthStatus(); // Refresh the status
                } else {
                    showError('token-error', data.message);
                }
            } catch (error) {
                hideLoading();
                showError('token-error', 'Network error. Please try again.');
            }
        }

        // Test connection
        async function testConnection() {
            showLoading('Testing connection...');

            try {
                const response = await fetch('/api/360dialog/auth/test');
                const data = await response.json();
                hideLoading();

                if (data.success) {
                    showSuccess('Connection test successful!');
                } else {
                    showError('token-error', 'Connection test failed: ' + data.message);
                }
            } catch (error) {
                hideLoading();
                showError('token-error', 'Network error during connection test.');
            }
        }

        // Disconnect
        async function disconnect() {
            if (!confirm('Are you sure you want to disconnect from 360Dialog?')) {
                return;
            }

            showLoading('Disconnecting...');

            try {
                const response = await fetch('/api/360dialog/auth/disconnect', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();
                hideLoading();

                if (data.success) {
                    showTokenInputState();
                    updateAuthStatus('disconnected', 'Successfully disconnected from 360Dialog');
                    showSuccess('Successfully disconnected from 360Dialog');
                } else {
                    showError('connection-error', 'Disconnect failed: ' + data.message);
                }
            } catch (error) {
                hideLoading();
                showError('connection-error', 'Network error during disconnect.');
            }
        }

        // Get account information
        async function getAccountInfo() {
            showLoading('Fetching account information...');

            try {
                const response = await fetch('/api/360dialog/account-info');
                const data = await response.json();
                hideLoading();

                if (data.success) {
                    showAccountInfoModal(data.data);
                } else {
                    showError('connection-error', 'Failed to fetch account info: ' + data.message);
                }
            } catch (error) {
                hideLoading();
                showError('connection-error', 'Network error while fetching account info.');
            }
        }

        // Sync templates
        async function syncTemplates() {
            showLoading('Syncing templates...');

            try {
                const response = await fetch('/api/360dialog/sync-templates', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();
                hideLoading();

                if (data.success) {
                    showSuccess(`Templates synced successfully! ${data.templates_count || 0} templates found.`);
                } else {
                    showError('connection-error', 'Template sync failed: ' + data.message);
                }
            } catch (error) {
                hideLoading();
                showError('connection-error', 'Network error during template sync.');
            }
        }

        // Sync traffic data
        async function syncTraffic() {
            showLoading('Syncing traffic data...');

            try {
                const response = await fetch('/api/360dialog/sync-traffic', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();
                hideLoading();

                if (data.success) {
                    showSuccess('Traffic data synced successfully!');
                } else {
                    showError('connection-error', 'Traffic sync failed: ' + data.message);
                }
            } catch (error) {
                hideLoading();
                showError('connection-error', 'Network error during traffic sync.');
            }
        }

        // Send test message
        async function sendTestMessage() {
            const phone = document.getElementById('test_phone').value.trim();
            const message = document.getElementById('test_message').value.trim();

            if (!validateTestMessage(phone, message)) {
                return;
            }

            showLoading('Sending test message...');

            try {
                const response = await fetch('/api/360dialog/send-test-message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ to: phone, message: message })
                });

                const data = await response.json();
                hideLoading();

                if (data.success) {
                    hideTestMessageModal();
                    showSuccess('Test message sent successfully!');
                } else {
                    showError('test-message-error', 'Failed to send message: ' + data.message);
                }
            } catch (error) {
                hideLoading();
                showError('test-message-error', 'Network error while sending message.');
            }
        }

        // UI Helper Functions
        function showTokenInputState() {
            document.getElementById('token-input-section').classList.remove('hidden');
            document.getElementById('connected-section').classList.add('hidden');
            document.getElementById('bearer_token').value = '';
            hideError('token-error');
        }

        function showConnectedState(data) {
            document.getElementById('token-input-section').classList.add('hidden');
            document.getElementById('connected-section').classList.remove('hidden');

            const connectionDetails = document.getElementById('connection-details');
            connectionDetails.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <span class="text-sm font-medium text-gray-600">Status:</span>
                        <span class="text-sm text-gray-900">${data.status || 'Connected'}</span>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-600">Last Sync:</span>
                        <span class="text-sm text-gray-900">${data.last_sync || 'Never'}</span>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-600">Token Length:</span>
                        <span class="text-sm text-gray-900">${data.token_length || 0} characters</span>
                    </div>
                </div>
            `;

            updateAuthStatus('authenticated', data.message || 'Successfully connected to 360Dialog');
        }

        function showTokenInput() {
            showTokenInputState();
        }

        function updateAuthStatus(status, message) {
            const authStatus = document.getElementById('auth-status');
            let statusClass, icon;

            switch(status) {
                case 'authenticated':
                    statusClass = 'bg-green-100 border-green-400 text-green-700';
                    icon = 'fa-check-circle';
                    break;
                case 'disconnected':
                    statusClass = 'bg-yellow-100 border-yellow-400 text-yellow-700';
                    icon = 'fa-exclamation-triangle';
                    break;
                default:
                    statusClass = 'bg-red-100 border-red-400 text-red-700';
                    icon = 'fa-times-circle';
            }

            authStatus.innerHTML = `
                <div class="border rounded-lg p-4 ${statusClass}">
                    <div class="flex items-center">
                        <i class="fas ${icon} mr-2"></i>
                        <span class="font-medium">${message}</span>
                    </div>
                </div>
            `;
        }

        // Modal Functions
        function showTestMessageModal() {
            document.getElementById('test-message-modal').classList.remove('hidden');
            document.getElementById('test_phone').focus();
        }

        function hideTestMessageModal() {
            document.getElementById('test-message-modal').classList.add('hidden');
            document.getElementById('test_phone').value = '';
            document.getElementById('test_message').value = 'Hello! This is a test message from 360Dialog integration.';
            hideError('test-message-error');
        }

        function showAccountInfoModal(accountData) {
            // Create a simple alert for now - in a real app, you'd create a proper modal
            const info = JSON.stringify(accountData, null, 2);
            alert('Account Information:\n\n' + info);
        }

        // Validation Functions
        function validateToken(token) {
            hideError('token-error');

            if (!token) {
                showError('token-error', 'Bearer token is required');
                return false;
            }

            if (token.length < 20) {
                showError('token-error', 'Bearer token must be at least 20 characters long');
                return false;
            }

            if (token.length > 5000) {
                showError('token-error', 'Bearer token must not exceed 5000 characters');
                return false;
            }

            if (!/^[A-Za-z0-9\-_\.]+$/.test(token)) {
                showError('token-error', 'Bearer token contains invalid characters');
                return false;
            }

            return true;
        }

        function validateTestMessage(phone, message) {
            hideError('test-message-error');

            if (!phone) {
                showError('test-message-error', 'Phone number is required');
                return false;
            }

            const phoneRegex = /^\+?[1-9]\d{1,14}$/;
            if (!phoneRegex.test(phone)) {
                showError('test-message-error', 'Please enter a valid phone number in international format');
                return false;
            }

            if (!message) {
                showError('test-message-error', 'Message content is required');
                return false;
            }

            if (message.length > 1000) {
                showError('test-message-error', 'Message must not exceed 1000 characters');
                return false;
            }

            return true;
        }

        // Utility Functions
        function toggleTokenVisibility() {
            const tokenInput = document.getElementById('bearer_token');
            const toggleBtn = document.getElementById('toggle-token-visibility');
            const icon = toggleBtn.querySelector('i');

            if (tokenInput.type === 'password') {
                tokenInput.type = 'text';
                icon.className = 'fas fa-eye-slash text-gray-400 hover:text-gray-600';
            } else {
                tokenInput.type = 'password';
                icon.className = 'fas fa-eye text-gray-400 hover:text-gray-600';
            }
        }

        function formatPhoneNumber(e) {
            let value = e.target.value.replace(/[^\d+]/g, '');
            if (value && !value.startsWith('+')) {
                value = '+' + value;
            }
            e.target.value = value;
        }

        function showLoading(text) {
            document.getElementById('loading-text').textContent = text;
            document.getElementById('loading-overlay').classList.remove('hidden');
        }

        function hideLoading() {
            document.getElementById('loading-overlay').classList.add('hidden');
        }

        function showError(elementId, message) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.classList.remove('hidden');
        }

        function hideError(elementId) {
            const element = document.getElementById(elementId);
            element.classList.add('hidden');
        }

        function showSuccess(message) {
            // Create a temporary success message
            const successDiv = document.createElement('div');
            successDiv.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50';
            successDiv.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(successDiv);

            setTimeout(() => {
                successDiv.remove();
            }, 5000);
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale0f1cdd055772eb1d4a99981c240763e)): ?>
<?php $attributes = $__attributesOriginale0f1cdd055772eb1d4a99981c240763e; ?>
<?php unset($__attributesOriginale0f1cdd055772eb1d4a99981c240763e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale0f1cdd055772eb1d4a99981c240763e)): ?>
<?php $component = $__componentOriginale0f1cdd055772eb1d4a99981c240763e; ?>
<?php unset($__componentOriginale0f1cdd055772eb1d4a99981c240763e); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/Herd/admin/resources/views/settings/360dialog.blade.php ENDPATH**/ ?>