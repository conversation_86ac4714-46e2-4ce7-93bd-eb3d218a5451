<?php if (isset($component)) { $__componentOriginale0f1cdd055772eb1d4a99981c240763e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale0f1cdd055772eb1d4a99981c240763e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h1 class="text-2xl font-bold text-gray-900">Taqnyat Settings</h1>
        <p class="text-gray-600">Configure your Taqnyat OTP authentication</p>
     <?php $__env->endSlot(); ?>

    <!-- Authentication Status -->
    <div id="auth-status" class="mb-6">
        <!-- Will be populated by JavaScript -->
    </div>

    <!-- OTP Authentication Section -->
    <div class="bg-white rounded-lg shadow p-6">
        <!-- Step 1: User Phone Display and OTP Request -->
        <div id="phone-step" class="space-y-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-mobile-alt text-blue-600 mr-2"></i>
                    OTP Authentication
                </h3>
                <p class="text-gray-600 mb-4">Request an OTP to authenticate with Taqnyat using your account phone number.</p>

                <div class="max-w-md">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Your Phone Number</label>
                    <div id="user-phone-display" class="w-full border border-gray-200 rounded-md px-3 py-2 bg-gray-50 text-gray-700 font-mono">
                        <!-- Will be populated by JavaScript -->
                    </div>
                    <p class="text-xs text-gray-500 mt-1">This phone number is linked to your account and will be used for OTP verification.</p>
                    <div id="phone-error" class="text-red-500 text-sm mt-1 hidden"></div>
                    <div id="no-phone-warning" class="text-amber-600 text-sm mt-1 hidden">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        No phone number found in your account. Please update your profile to add a phone number.
                    </div>
                </div>

                <div class="mt-4">
                    <button type="button" id="request-otp-btn"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Request OTP
                    </button>

                    <button type="button" id="update-phone-btn"
                            class="ml-3 bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg font-medium hidden">
                        <i class="fas fa-edit mr-2"></i>
                        Update Phone Number
                    </button>
                </div>
            </div>
        </div>

        <!-- Step 2: OTP Verification -->
        <div id="otp-step" class="space-y-6 hidden">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-key text-green-600 mr-2"></i>
                    Verify OTP
                </h3>
                <p class="text-gray-600 mb-4">Enter the 6-digit OTP sent to your phone number.</p>

                <div class="max-w-md">
                    <label class="block text-sm font-medium text-gray-700 mb-2">OTP Code</label>
                    <input type="text" id="otp_code" maxlength="6"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 text-center text-lg font-mono"
                           placeholder="123456"
                           pattern="^\d{6}$">
                    <div id="otp-timer" class="text-sm text-gray-500 mt-1"></div>
                    <div id="otp-error" class="text-red-500 text-sm mt-1 hidden"></div>
                    <div id="demo-hint" class="text-blue-600 text-sm mt-1 hidden">
                        <i class="fas fa-info-circle mr-1"></i>
                        Demo Mode: Use OTP <strong>123456</strong>
                    </div>
                </div>

                <div class="mt-4 flex space-x-3">
                    <button type="button" id="verify-otp-btn"
                            class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed">
                        <i class="fas fa-check mr-2"></i>
                        Verify OTP
                    </button>

                    <button type="button" id="resend-otp-btn"
                            class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed">
                        <i class="fas fa-redo mr-2"></i>
                        Resend OTP
                    </button>

                    <button type="button" id="cancel-otp-btn"
                            class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium">
                        <i class="fas fa-times mr-2"></i>
                        Cancel
                    </button>
                </div>
            </div>
        </div>

        <!-- Step 3: Authenticated State -->
        <div id="authenticated-step" class="space-y-6 hidden">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-check-circle text-green-600 mr-2"></i>
                    Authentication Successful
                </h3>
                <p class="text-gray-600 mb-4">You are successfully authenticated with Taqnyat.</p>

                <div id="auth-details" class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                    <!-- Will be populated with authentication details -->
                </div>

                <div class="flex space-x-3">
                    <button type="button" id="test-connection-btn"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium">
                        <i class="fas fa-plug mr-2"></i>
                        Test Connection
                    </button>

                    <button type="button" id="logout-btn"
                            class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium">
                        <i class="fas fa-sign-out-alt mr-2"></i>
                        Logout
                    </button>
                </div>
            </div>
        </div>

    </div>

    <!-- Phone Update Modal -->
    <div id="phone-update-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Update Phone Number</h3>

            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                    <input type="tel" id="new_phone_number"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="+966501234567">
                    <p class="text-xs text-gray-500 mt-1">Enter phone number in international format (e.g., +966501234567)</p>
                </div>

                <div id="phone-update-error" class="text-red-500 text-sm hidden"></div>
            </div>

            <div class="mt-6 flex space-x-3">
                <button type="button" id="save-phone-btn"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                    <i class="fas fa-save mr-2"></i>
                    Save Phone Number
                </button>

                <button type="button" id="cancel-phone-btn"
                        class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium">
                    Cancel
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
            <div class="flex items-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
                <span id="loading-text">Processing...</span>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let currentSessionId = null;
        let otpTimer = null;
        let otpExpiresAt = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthStatus();
            setupEventListeners();
        });

        // Setup event listeners
        function setupEventListeners() {
            document.getElementById('request-otp-btn').addEventListener('click', requestOtp);
            document.getElementById('verify-otp-btn').addEventListener('click', verifyOtp);
            document.getElementById('resend-otp-btn').addEventListener('click', resendOtp);
            document.getElementById('cancel-otp-btn').addEventListener('click', cancelOtp);
            document.getElementById('test-connection-btn').addEventListener('click', testConnection);
            document.getElementById('logout-btn').addEventListener('click', logout);
            document.getElementById('update-phone-btn').addEventListener('click', showPhoneUpdateModal);

            // Phone update modal
            document.getElementById('save-phone-btn').addEventListener('click', savePhoneNumber);
            document.getElementById('cancel-phone-btn').addEventListener('click', hidePhoneUpdateModal);

            // Auto-format OTP input
            document.getElementById('otp_code').addEventListener('input', formatOtpInput);
            document.getElementById('otp_code').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    verifyOtp();
                }
            });

            // Auto-format phone number in modal
            document.getElementById('new_phone_number').addEventListener('input', formatPhoneNumberInput);
        }

        // Check authentication status
        async function checkAuthStatus() {
            try {
                const response = await fetch('/api/taqnyat/auth/status');
                const data = await response.json();

                if (data.authenticated) {
                    showAuthenticatedState(data);
                } else {
                    showPhoneStep(data);
                }
            } catch (error) {
                console.error('Error checking auth status:', error);
                showPhoneStep({});
            }
        }

        // Request OTP
        async function requestOtp() {
            showLoading('Sending OTP...');

            try {
                const response = await fetch('/api/taqnyat/auth/request-otp', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();
                hideLoading();

                if (data.success) {
                    currentSessionId = data.session_id;
                    otpExpiresAt = new Date(Date.now() + (data.expires_in * 1000));

                    showOtpStep();
                    startOtpTimer();

                    if (data.demo_otp) {
                        document.getElementById('demo-hint').classList.remove('hidden');
                    }

                    showSuccess('OTP sent successfully to ' + data.phone_number);
                } else {
                    showError('phone-error', data.message);
                }
            } catch (error) {
                hideLoading();
                showError('phone-error', 'Network error. Please try again.');
            }
        }

        // Verify OTP
        async function verifyOtp() {
            const otpCode = document.getElementById('otp_code').value.trim();

            if (!validateOtp(otpCode)) {
                return;
            }

            showLoading('Verifying OTP...');

            try {
                const response = await fetch('/api/taqnyat/auth/verify-otp', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        session_id: currentSessionId,
                        otp: otpCode
                    })
                });

                const data = await response.json();
                hideLoading();

                if (data.success) {
                    stopOtpTimer();
                    showAuthenticatedState(data);
                    showSuccess('Authentication successful!');
                } else {
                    showError('otp-error', data.message);

                    if (data.attempts_remaining !== undefined) {
                        showError('otp-error', data.message + ` (${data.attempts_remaining} attempts remaining)`);
                    }

                    if (data.error === 'SESSION_EXPIRED' || data.error === 'MAX_ATTEMPTS_EXCEEDED') {
                        setTimeout(() => {
                            cancelOtp();
                        }, 3000);
                    }
                }
            } catch (error) {
                hideLoading();
                showError('otp-error', 'Network error. Please try again.');
            }
        }

        // Resend OTP
        function resendOtp() {
            cancelOtp();
            setTimeout(() => {
                requestOtp();
            }, 100);
        }

        // Cancel OTP
        function cancelOtp() {
            stopOtpTimer();
            currentSessionId = null;
            otpExpiresAt = null;

            document.getElementById('otp_code').value = '';
            hideError('otp-error');
            document.getElementById('demo-hint').classList.add('hidden');

            showPhoneStep();
        }

        // Test connection
        async function testConnection() {
            showLoading('Testing connection...');

            try {
                const response = await fetch('/api/taqnyat/auth/test');
                const data = await response.json();
                hideLoading();

                if (data.success) {
                    showSuccess('Connection test successful!');
                } else {
                    showError('auth-error', 'Connection test failed: ' + data.message);
                }
            } catch (error) {
                hideLoading();
                showError('auth-error', 'Network error during connection test.');
            }
        }

        // Logout
        async function logout() {
            if (!confirm('Are you sure you want to logout from Taqnyat?')) {
                return;
            }

            showLoading('Logging out...');

            try {
                const response = await fetch('/api/taqnyat/auth/logout', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();
                hideLoading();

                if (data.success) {
                    showPhoneStep();
                    showSuccess('Successfully logged out from Taqnyat');
                } else {
                    showError('auth-error', 'Logout failed: ' + data.message);
                }
            } catch (error) {
                hideLoading();
                showError('auth-error', 'Network error during logout.');
            }
        }

        // UI Helper Functions
        function showPhoneStep(authData = {}) {
            document.getElementById('phone-step').classList.remove('hidden');
            document.getElementById('otp-step').classList.add('hidden');
            document.getElementById('authenticated-step').classList.add('hidden');

            // Display user's phone number
            const userPhoneDisplay = document.getElementById('user-phone-display');
            const noPhoneWarning = document.getElementById('no-phone-warning');
            const requestOtpBtn = document.getElementById('request-otp-btn');
            const updatePhoneBtn = document.getElementById('update-phone-btn');

            if (authData.user_phone) {
                userPhoneDisplay.textContent = authData.user_phone;
                noPhoneWarning.classList.add('hidden');
                requestOtpBtn.disabled = false;
                updatePhoneBtn.classList.add('hidden');
            } else {
                userPhoneDisplay.textContent = 'No phone number configured';
                noPhoneWarning.classList.remove('hidden');
                requestOtpBtn.disabled = true;
                updatePhoneBtn.classList.remove('hidden');
            }

            updateAuthStatus('not_authenticated', 'Not authenticated with Taqnyat');
        }

        function showOtpStep() {
            document.getElementById('phone-step').classList.add('hidden');
            document.getElementById('otp-step').classList.remove('hidden');
            document.getElementById('authenticated-step').classList.add('hidden');
            document.getElementById('otp_code').focus();
        }

        function showAuthenticatedState(data) {
            document.getElementById('phone-step').classList.add('hidden');
            document.getElementById('otp-step').classList.add('hidden');
            document.getElementById('authenticated-step').classList.remove('hidden');

            const authDetails = document.getElementById('auth-details');
            authDetails.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <span class="text-sm font-medium text-gray-600">Phone Number:</span>
                        <span class="text-sm text-gray-900">${data.phone_number || 'N/A'}</span>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-600">Authenticated At:</span>
                        <span class="text-sm text-gray-900">${data.authenticated_at ? new Date(data.authenticated_at).toLocaleString() : 'N/A'}</span>
                    </div>
                </div>
            `;

            updateAuthStatus('authenticated', 'Successfully authenticated with Taqnyat');
        }

        function updateAuthStatus(status, message) {
            const authStatus = document.getElementById('auth-status');
            const statusClass = status === 'authenticated' ? 'bg-green-100 border-green-400 text-green-700' : 'bg-red-100 border-red-400 text-red-700';
            const icon = status === 'authenticated' ? 'fa-check-circle' : 'fa-times-circle';

            authStatus.innerHTML = `
                <div class="border rounded-lg p-4 ${statusClass}">
                    <div class="flex items-center">
                        <i class="fas ${icon} mr-2"></i>
                        <span class="font-medium">${message}</span>
                    </div>
                </div>
            `;
        }

        // Validation Functions

        function validateOtp(otp) {
            hideError('otp-error');

            if (!otp) {
                showError('otp-error', 'OTP is required');
                return false;
            }

            if (!/^\d{6}$/.test(otp)) {
                showError('otp-error', 'OTP must be exactly 6 digits');
                return false;
            }

            return true;
        }

        // Utility Functions

        function formatOtpInput(e) {
            e.target.value = e.target.value.replace(/\D/g, '').substring(0, 6);
        }

        function startOtpTimer() {
            updateOtpTimer();
            otpTimer = setInterval(updateOtpTimer, 1000);
        }

        function stopOtpTimer() {
            if (otpTimer) {
                clearInterval(otpTimer);
                otpTimer = null;
            }
        }

        function updateOtpTimer() {
            if (!otpExpiresAt) return;

            const now = new Date();
            const timeLeft = Math.max(0, Math.floor((otpExpiresAt - now) / 1000));

            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;

            const timerElement = document.getElementById('otp-timer');
            if (timeLeft > 0) {
                timerElement.textContent = `OTP expires in ${minutes}:${seconds.toString().padStart(2, '0')}`;
                timerElement.className = 'text-sm text-gray-500 mt-1';
            } else {
                timerElement.textContent = 'OTP has expired. Please request a new one.';
                timerElement.className = 'text-sm text-red-500 mt-1';
                stopOtpTimer();

                // Disable verify button
                document.getElementById('verify-otp-btn').disabled = true;
            }
        }

        function showLoading(text) {
            document.getElementById('loading-text').textContent = text;
            document.getElementById('loading-overlay').classList.remove('hidden');
        }

        function hideLoading() {
            document.getElementById('loading-overlay').classList.add('hidden');
        }

        function showError(elementId, message) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.classList.remove('hidden');
        }

        function hideError(elementId) {
            const element = document.getElementById(elementId);
            element.classList.add('hidden');
        }

        function showSuccess(message) {
            // Create a temporary success message
            const successDiv = document.createElement('div');
            successDiv.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50';
            successDiv.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(successDiv);

            setTimeout(() => {
                successDiv.remove();
            }, 5000);
        }

        // Phone Update Functions
        function showPhoneUpdateModal() {
            document.getElementById('phone-update-modal').classList.remove('hidden');
            document.getElementById('new_phone_number').focus();
        }

        function hidePhoneUpdateModal() {
            document.getElementById('phone-update-modal').classList.add('hidden');
            document.getElementById('new_phone_number').value = '';
            hideError('phone-update-error');
        }

        async function savePhoneNumber() {
            const phoneNumber = document.getElementById('new_phone_number').value.trim();

            if (!validatePhoneNumberFormat(phoneNumber)) {
                return;
            }

            showLoading('Updating phone number...');

            try {
                const response = await fetch('/api/user/update-phone', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ phone: phoneNumber })
                });

                const data = await response.json();
                hideLoading();

                if (data.success) {
                    hidePhoneUpdateModal();
                    showSuccess('Phone number updated successfully!');
                    // Refresh the auth status to show the new phone number
                    checkAuthStatus();
                } else {
                    showError('phone-update-error', data.message);
                }
            } catch (error) {
                hideLoading();
                showError('phone-update-error', 'Network error. Please try again.');
            }
        }

        function validatePhoneNumberFormat(phoneNumber) {
            hideError('phone-update-error');

            if (!phoneNumber) {
                showError('phone-update-error', 'Phone number is required');
                return false;
            }

            const phoneRegex = /^\+?[1-9]\d{1,14}$/;
            if (!phoneRegex.test(phoneNumber)) {
                showError('phone-update-error', 'Please enter a valid phone number in international format');
                return false;
            }

            return true;
        }

        function formatPhoneNumberInput(e) {
            let value = e.target.value.replace(/[^\d+]/g, '');
            if (value && !value.startsWith('+')) {
                value = '+' + value;
            }
            e.target.value = value;
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale0f1cdd055772eb1d4a99981c240763e)): ?>
<?php $attributes = $__attributesOriginale0f1cdd055772eb1d4a99981c240763e; ?>
<?php unset($__attributesOriginale0f1cdd055772eb1d4a99981c240763e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale0f1cdd055772eb1d4a99981c240763e)): ?>
<?php $component = $__componentOriginale0f1cdd055772eb1d4a99981c240763e; ?>
<?php unset($__componentOriginale0f1cdd055772eb1d4a99981c240763e); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/Herd/admin/resources/views/settings/taqnyat.blade.php ENDPATH**/ ?>