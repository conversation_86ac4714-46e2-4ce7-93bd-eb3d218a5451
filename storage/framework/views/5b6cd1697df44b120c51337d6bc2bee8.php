<?php if (isset($component)) { $__componentOriginale0f1cdd055772eb1d4a99981c240763e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale0f1cdd055772eb1d4a99981c240763e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h1 class="text-2xl font-bold text-gray-900">WhatsApp Traffic</h1>
        <p class="text-gray-600">Monitor and analyze WhatsApp message traffic</p>
     <?php $__env->endSlot(); ?>

    <div id="loading-state" class="bg-white rounded-lg shadow p-8 text-center">
        <i class="fas fa-spinner fa-spin text-4xl text-gray-400 mb-4"></i>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Loading Traffic Data</h3>
        <p class="text-gray-600">Checking 360Dialog configuration...</p>
    </div>

    <div id="not-configured" class="bg-white rounded-lg shadow p-8 text-center hidden">
        <i class="fas fa-chart-line text-6xl text-gray-400 mb-4"></i>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Traffic Analytics</h3>
        <p class="text-gray-600 mb-6">View detailed analytics of your WhatsApp message traffic and engagement metrics.</p>
        <a href="<?php echo e(route('settings.360dialog')); ?>" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium">
            Configure Integration
        </a>
    </div>

    <div id="configured" class="hidden">
        <!-- Traffic Overview Section -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-dollar-sign text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Current Balance</p>
                        <p class="text-2xl font-semibold text-gray-900" id="current-balance">Loading...</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-chart-line text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Template Cost</p>
                        <p class="text-2xl font-semibold text-gray-900" id="template-cost">Loading...</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fas fa-calendar text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Granularity</p>
                        <p class="text-2xl font-semibold text-gray-900" id="granularity">Loading...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage Details Section -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Usage Details</h3>
            <div id="usage-content" class="space-y-4">
                <!-- Usage data will be loaded here -->
            </div>
        </div>
    </div>

    <div id="error-state" class="bg-white rounded-lg shadow p-8 text-center hidden">
        <i class="fas fa-exclamation-triangle text-6xl text-red-500 mb-4"></i>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Connection Error</h3>
        <p class="text-gray-600 mb-6" id="error-message">Unable to connect to 360Dialog API.</p>
        <button onclick="loadTrafficData()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium mr-4">
            Retry
        </button>
        <a href="<?php echo e(route('settings.360dialog')); ?>" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium">
            Check Configuration
        </a>
    </div>

    <script>
        async function loadTrafficData() {
            try {
                // Show loading state
                document.getElementById('loading-state').classList.remove('hidden');
                document.getElementById('not-configured').classList.add('hidden');
                document.getElementById('configured').classList.add('hidden');
                document.getElementById('error-state').classList.add('hidden');

                // Check authentication status first
                const authResponse = await fetch('/api/360dialog/auth/status', {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const authData = await authResponse.json();

                if (!authData.authenticated) {
                    // Not configured
                    document.getElementById('loading-state').classList.add('hidden');
                    document.getElementById('not-configured').classList.remove('hidden');
                    return;
                }

                // Load traffic data
                const trafficResponse = await fetch('/api/360dialog/traffic-data', {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const trafficData = await trafficResponse.json();

                if (trafficData.success) {
                    displayTrafficData(trafficData.data);
                    // Show configured state
                    document.getElementById('loading-state').classList.add('hidden');
                    document.getElementById('configured').classList.remove('hidden');
                } else {
                    throw new Error(trafficData.message || 'Failed to load traffic data');
                }

            } catch (error) {
                console.error('Error loading traffic data:', error);
                document.getElementById('loading-state').classList.add('hidden');
                document.getElementById('error-state').classList.remove('hidden');
                document.getElementById('error-message').textContent = error.message || 'Failed to load traffic data. Please try again.';
            }
        }

        function displayTrafficData(data) {
            // Update overview cards
            document.getElementById('current-balance').textContent = `${data.balance || 0} ${(data.currency || 'USD').toUpperCase()}`;
            document.getElementById('template-cost').textContent = `${data.estimated_template_cost || 0} ${(data.currency || 'USD').toUpperCase()}`;
            document.getElementById('granularity').textContent = (data.granularity || 'month').charAt(0).toUpperCase() + (data.granularity || 'month').slice(1);

            // Display usage data
            const usageContent = document.getElementById('usage-content');
            const usage = data.usage || [];

            if (usage.length === 0) {
                usageContent.innerHTML = '<p class="text-gray-600">No usage data available.</p>';
                return;
            }

            usageContent.innerHTML = `
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Messages</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business Initiated</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Free Tier</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Paid</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${usage.slice(0, 10).map(period => `
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        ${new Date(period.period_date).toLocaleDateString()}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        ${period.quantity || 0}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        ${period.business_initiated_quantity || 0}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        ${period.free_quantity || 0}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        ${period.paid_quantity || 0}
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                ${usage.length > 10 ? `<p class="text-sm text-gray-600 mt-4">Showing 10 of ${usage.length} periods</p>` : ''}
            `;
        }

        // Load data when page loads
        document.addEventListener('DOMContentLoaded', loadTrafficData);
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale0f1cdd055772eb1d4a99981c240763e)): ?>
<?php $attributes = $__attributesOriginale0f1cdd055772eb1d4a99981c240763e; ?>
<?php unset($__attributesOriginale0f1cdd055772eb1d4a99981c240763e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale0f1cdd055772eb1d4a99981c240763e)): ?>
<?php $component = $__componentOriginale0f1cdd055772eb1d4a99981c240763e; ?>
<?php unset($__componentOriginale0f1cdd055772eb1d4a99981c240763e); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/Herd/admin/resources/views/360dialog/traffic.blade.php ENDPATH**/ ?>