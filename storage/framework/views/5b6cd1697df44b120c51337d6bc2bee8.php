<?php if (isset($component)) { $__componentOriginale0f1cdd055772eb1d4a99981c240763e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale0f1cdd055772eb1d4a99981c240763e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h1 class="text-2xl font-bold text-gray-900">WhatsApp Traffic</h1>
        <p class="text-gray-600">Monitor and analyze WhatsApp message traffic</p>
     <?php $__env->endSlot(); ?>

    <div id="loading-state" class="bg-white rounded-lg shadow p-8 text-center">
        <i class="fas fa-spinner fa-spin text-4xl text-gray-400 mb-4"></i>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Loading Traffic Data</h3>
        <p class="text-gray-600">Checking 360Dialog configuration...</p>
    </div>

    <div id="not-configured" class="bg-white rounded-lg shadow p-8 text-center hidden">
        <i class="fas fa-chart-line text-6xl text-gray-400 mb-4"></i>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Traffic Analytics</h3>
        <p class="text-gray-600 mb-6">View detailed analytics of your WhatsApp message traffic and engagement metrics.</p>
        <a href="<?php echo e(route('settings.360dialog')); ?>" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium">
            Configure Integration
        </a>
    </div>

    <div id="configured" class="hidden">
        <!-- Channel Selection Section -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Channel Selection</h3>
                <button onclick="refreshChannelList()" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                    <i class="fas fa-refresh mr-1"></i> Refresh
                </button>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex-1">
                    <label for="channel-select" class="block text-sm font-medium text-gray-700 mb-2">Select Channel:</label>
                    <select id="channel-select" class="w-full border-gray-300 rounded-md shadow-sm" onchange="loadTrafficForSelectedChannel()">
                        <option value="">Loading channels...</option>
                    </select>
                </div>
                <div class="text-sm text-gray-600" id="selected-channel-info">
                    <!-- Selected channel info will be displayed here -->
                </div>
            </div>
        </div>

        <!-- Traffic Overview Section -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-dollar-sign text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Current Balance</p>
                        <p class="text-2xl font-semibold text-gray-900" id="current-balance">Loading...</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-chart-line text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Template Cost</p>
                        <p class="text-2xl font-semibold text-gray-900" id="template-cost">Loading...</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fas fa-calendar text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Granularity</p>
                        <p class="text-2xl font-semibold text-gray-900" id="granularity">Loading...</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                        <i class="fas fa-envelope text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Messages</p>
                        <p class="text-2xl font-semibold text-gray-900" id="total-messages">Loading...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage Details Section -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Usage Details</h3>
            <div id="usage-content" class="space-y-4">
                <!-- Usage data will be loaded here -->
            </div>
        </div>
    </div>

    <div id="error-state" class="bg-white rounded-lg shadow p-8 text-center hidden">
        <i class="fas fa-exclamation-triangle text-6xl text-red-500 mb-4"></i>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Connection Error</h3>
        <p class="text-gray-600 mb-6" id="error-message">Unable to connect to 360Dialog API.</p>
        <button onclick="loadTrafficData()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium mr-4">
            Retry
        </button>
        <a href="<?php echo e(route('settings.360dialog')); ?>" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium">
            Check Configuration
        </a>
    </div>

    <script>
        let availableChannels = [];
        let selectedChannel = null;

        async function loadTrafficData() {
            try {
                // Show loading state
                document.getElementById('loading-state').classList.remove('hidden');
                document.getElementById('not-configured').classList.add('hidden');
                document.getElementById('configured').classList.add('hidden');
                document.getElementById('error-state').classList.add('hidden');

                // Check authentication status first
                const authResponse = await fetch('/api/360dialog/auth/status', {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const authData = await authResponse.json();

                if (!authData.authenticated) {
                    // Not configured
                    document.getElementById('loading-state').classList.add('hidden');
                    document.getElementById('not-configured').classList.remove('hidden');
                    return;
                }

                // Load available channels first
                await loadAvailableChannels();

                // Check if there's a pre-selected channel from localStorage
                const storedChannel = localStorage.getItem('selectedChannel');
                if (storedChannel) {
                    try {
                        selectedChannel = JSON.parse(storedChannel);
                        localStorage.removeItem('selectedChannel'); // Clear after use
                    } catch (e) {
                        console.warn('Failed to parse stored channel:', e);
                    }
                }

                // Show configured state
                document.getElementById('loading-state').classList.add('hidden');
                document.getElementById('configured').classList.remove('hidden');

                // Load traffic data for selected channel if available
                if (selectedChannel && selectedChannel.id) {
                    selectChannelById(selectedChannel.id);
                    await loadTrafficForSelectedChannel();
                }

            } catch (error) {
                console.error('Error loading traffic data:', error);
                document.getElementById('loading-state').classList.add('hidden');
                document.getElementById('error-state').classList.remove('hidden');
                document.getElementById('error-message').textContent = error.message || 'Failed to load traffic data. Please try again.';
            }
        }

        async function loadAvailableChannels() {
            try {
                const response = await fetch('/api/360dialog/available-channels', {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    availableChannels = data.channels;
                    populateChannelSelect();
                } else {
                    console.error('Failed to load channels:', data.message);
                    document.getElementById('channel-select').innerHTML = '<option value="">Failed to load channels</option>';
                }
            } catch (error) {
                console.error('Error loading channels:', error);
                document.getElementById('channel-select').innerHTML = '<option value="">Error loading channels</option>';
            }
        }

        function populateChannelSelect() {
            const select = document.getElementById('channel-select');

            if (availableChannels.length === 0) {
                select.innerHTML = '<option value="">No channels available</option>';
                return;
            }

            let options = '<option value="">Select a channel...</option>';
            availableChannels.forEach(channel => {
                const displayName = `${channel.client_name} (${channel.phone_number}) - ${channel.status}`;
                options += `<option value="${channel.channel_id}" data-client-id="${channel.client_id}" data-project-id="${channel.project_id}" data-partner-id="${channel.partner_id}">${displayName}</option>`;
            });

            select.innerHTML = options;
        }

        function selectChannelById(channelId) {
            const select = document.getElementById('channel-select');
            select.value = channelId;
            updateSelectedChannelInfo();
        }

        function updateSelectedChannelInfo() {
            const select = document.getElementById('channel-select');
            const selectedOption = select.options[select.selectedIndex];
            const infoDiv = document.getElementById('selected-channel-info');

            if (select.value && selectedOption) {
                const channel = availableChannels.find(c => c.channel_id === select.value);
                if (channel) {
                    infoDiv.innerHTML = `
                        <div class="text-xs">
                            <div><strong>Client:</strong> ${channel.client_name}</div>
                            <div><strong>Phone:</strong> ${channel.phone_number}</div>
                            <div><strong>Status:</strong> <span class="px-1 py-0.5 rounded text-xs ${channel.status === 'ready' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">${channel.status}</span></div>
                        </div>
                    `;
                }
            } else {
                infoDiv.innerHTML = '';
            }
        }

        async function loadTrafficForSelectedChannel() {
            const select = document.getElementById('channel-select');
            const selectedOption = select.options[select.selectedIndex];

            if (!select.value || !selectedOption) {
                // Clear traffic data display
                clearTrafficData();
                return;
            }

            updateSelectedChannelInfo();

            try {
                const channelId = select.value;
                const clientId = selectedOption.getAttribute('data-client-id');
                const projectId = selectedOption.getAttribute('data-project-id');
                const partnerId = selectedOption.getAttribute('data-partner-id');

                const response = await fetch(`/api/360dialog/traffic-data?channel_id=${channelId}&client_id=${clientId}&project_id=${projectId}&partner_id=${partnerId}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const trafficData = await response.json();

                if (trafficData.success) {
                    displayTrafficData(trafficData.data);
                } else {
                    throw new Error(trafficData.message || 'Failed to load traffic data');
                }
            } catch (error) {
                console.error('Error loading traffic data:', error);
                clearTrafficData();
                alert('Failed to load traffic data for selected channel. Please try again.');
            }
        }

        function clearTrafficData() {
            document.getElementById('current-balance').textContent = 'No data';
            document.getElementById('template-cost').textContent = 'No data';
            document.getElementById('granularity').textContent = 'No data';
            document.getElementById('total-messages').textContent = 'No data';
            document.getElementById('usage-content').innerHTML = '<p class="text-gray-600">Select a channel to view traffic data.</p>';
        }

        function refreshChannelList() {
            loadAvailableChannels();
        }

        function displayTrafficData(data) {
            // Update overview cards
            document.getElementById('current-balance').textContent = `${data.balance || 0} ${(data.currency || 'USD').toUpperCase()}`;
            document.getElementById('template-cost').textContent = `${data.estimated_template_cost || 0} ${(data.currency || 'USD').toUpperCase()}`;
            document.getElementById('granularity').textContent = (data.granularity || 'month').charAt(0).toUpperCase() + (data.granularity || 'month').slice(1);

            // Calculate total messages from usage data
            const usage = data.usage || [];
            const totalMessages = usage.reduce((sum, period) => sum + (period.quantity || 0), 0);
            document.getElementById('total-messages').textContent = totalMessages.toLocaleString();

            // Display usage data
            const usageContent = document.getElementById('usage-content');

            if (usage.length === 0) {
                usageContent.innerHTML = '<p class="text-gray-600">No usage data available.</p>';
                return;
            }

            usageContent.innerHTML = `
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Free</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Paid</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business Init.</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User Init.</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Authentication</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Marketing</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Utility</th>
                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Price</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${usage.slice(0, 10).map(period => `
                                <tr class="hover:bg-gray-50">
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                                        ${new Date(period.period_date).toLocaleDateString()}
                                    </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <span class="font-semibold">${period.quantity || 0}</span>
                                    </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-green-600">
                                        ${period.free_quantity || 0}
                                        ${period.free_tier ? `<br><span class="text-xs text-gray-500">(Tier: ${period.free_tier})</span>` : ''}
                                    </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-red-600">
                                        ${period.paid_quantity || 0}
                                    </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <div>${period.business_initiated_quantity || 0}</div>
                                        <div class="text-xs text-gray-500">Paid: ${period.business_initiated_paid_quantity || 0}</div>
                                        <div class="text-xs text-green-600">$${(period.business_initiated_price || 0).toFixed(3)}</div>
                                    </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <div>${period.user_initiated_quantity || 0}</div>
                                        <div class="text-xs text-gray-500">Paid: ${period.user_initiated_paid_quantity || 0}</div>
                                        <div class="text-xs text-green-600">$${(period.user_initiated_price || 0).toFixed(3)}</div>
                                    </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <div>${period.authentication_quantity || 0}</div>
                                        <div class="text-xs text-gray-500">Paid: ${period.authentication_paid_quantity || 0}</div>
                                        <div class="text-xs text-green-600">$${(period.authentication_price || 0).toFixed(3)}</div>
                                    </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <div>${period.marketing_quantity || 0}</div>
                                        <div class="text-xs text-gray-500">Paid: ${period.marketing_paid_quantity || 0}</div>
                                        <div class="text-xs text-green-600">$${(period.marketing_price || 0).toFixed(3)}</div>
                                    </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <div>${period.service_quantity || 0}</div>
                                        <div class="text-xs text-gray-500">Paid: ${period.service_paid_quantity || 0}</div>
                                        <div class="text-xs text-green-600">$${(period.service_price || 0).toFixed(3)}</div>
                                    </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <div>${period.utility_quantity || 0}</div>
                                        <div class="text-xs text-gray-500">Paid: ${period.utility_paid_quantity || 0}</div>
                                        <div class="text-xs text-green-600">$${(period.utility_price || 0).toFixed(3)}</div>
                                    </td>
                                    <td class="px-3 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">
                                        $${(period.total_price || 0).toFixed(3)}
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                ${usage.length > 10 ? `<p class="text-sm text-gray-600 mt-4">Showing 10 of ${usage.length} periods</p>` : ''}

                <!-- Summary Statistics -->
                <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-blue-900 mb-2">Message Type Breakdown</h4>
                        <div class="space-y-1 text-sm">
                            <div class="flex justify-between">
                                <span class="text-blue-700">Business Initiated:</span>
                                <span class="font-medium">${usage.reduce((sum, p) => sum + (p.business_initiated_quantity || 0), 0)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-blue-700">User Initiated:</span>
                                <span class="font-medium">${usage.reduce((sum, p) => sum + (p.user_initiated_quantity || 0), 0)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-blue-700">Authentication:</span>
                                <span class="font-medium">${usage.reduce((sum, p) => sum + (p.authentication_quantity || 0), 0)}</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-green-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-green-900 mb-2">Cost Breakdown</h4>
                        <div class="space-y-1 text-sm">
                            <div class="flex justify-between">
                                <span class="text-green-700">Total Cost:</span>
                                <span class="font-medium">$${usage.reduce((sum, p) => sum + (p.total_price || 0), 0).toFixed(3)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-green-700">Free Messages:</span>
                                <span class="font-medium">${usage.reduce((sum, p) => sum + (p.free_quantity || 0), 0)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-green-700">Paid Messages:</span>
                                <span class="font-medium">${usage.reduce((sum, p) => sum + (p.paid_quantity || 0), 0)}</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-purple-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-purple-900 mb-2">Service Types</h4>
                        <div class="space-y-1 text-sm">
                            <div class="flex justify-between">
                                <span class="text-purple-700">Marketing:</span>
                                <span class="font-medium">${usage.reduce((sum, p) => sum + (p.marketing_quantity || 0), 0)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-purple-700">Service:</span>
                                <span class="font-medium">${usage.reduce((sum, p) => sum + (p.service_quantity || 0), 0)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-purple-700">Utility:</span>
                                <span class="font-medium">${usage.reduce((sum, p) => sum + (p.utility_quantity || 0), 0)}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Load data when page loads
        document.addEventListener('DOMContentLoaded', loadTrafficData);
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale0f1cdd055772eb1d4a99981c240763e)): ?>
<?php $attributes = $__attributesOriginale0f1cdd055772eb1d4a99981c240763e; ?>
<?php unset($__attributesOriginale0f1cdd055772eb1d4a99981c240763e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale0f1cdd055772eb1d4a99981c240763e)): ?>
<?php $component = $__componentOriginale0f1cdd055772eb1d4a99981c240763e; ?>
<?php unset($__componentOriginale0f1cdd055772eb1d4a99981c240763e); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/Herd/admin/resources/views/360dialog/traffic.blade.php ENDPATH**/ ?>