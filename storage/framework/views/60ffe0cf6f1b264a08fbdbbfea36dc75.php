<?php if (isset($component)) { $__componentOriginale0f1cdd055772eb1d4a99981c240763e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale0f1cdd055772eb1d4a99981c240763e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900"><?php echo e($business->name); ?></h1>
                <p class="text-gray-600"><?php echo e($business->industry ?? 'Business Details'); ?></p>
            </div>
            <div class="flex space-x-2">
                <a href="<?php echo e(route('businesses.edit', $business)); ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                    <i class="fas fa-edit mr-2"></i>
                    Edit
                </a>
                <a href="<?php echo e(route('businesses.index')); ?>" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Business Name</label>
                        <p class="text-gray-900 font-medium"><?php echo e($business->name); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Industry</label>
                        <p class="text-gray-900"><?php echo e($business->industry ?? 'Not specified'); ?></p>
                    </div>
                    
                    <?php if($business->description): ?>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-500">Description</label>
                            <p class="text-gray-900"><?php echo e($business->description); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <?php if($business->contact_person): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Contact Person</label>
                            <p class="text-gray-900"><?php echo e($business->contact_person); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($business->email): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Email</label>
                            <p class="text-gray-900">
                                <a href="mailto:<?php echo e($business->email); ?>" class="text-blue-600 hover:text-blue-800">
                                    <?php echo e($business->email); ?>

                                </a>
                            </p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($business->phone): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Phone</label>
                            <p class="text-gray-900">
                                <a href="tel:<?php echo e($business->phone); ?>" class="text-blue-600 hover:text-blue-800">
                                    <?php echo e($business->phone); ?>

                                </a>
                            </p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($business->website): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Website</label>
                            <p class="text-gray-900">
                                <a href="<?php echo e($business->website); ?>" target="_blank" class="text-blue-600 hover:text-blue-800">
                                    <?php echo e($business->website); ?>

                                    <i class="fas fa-external-link-alt ml-1 text-xs"></i>
                                </a>
                            </p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($business->address): ?>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-500">Address</label>
                            <p class="text-gray-900"><?php echo e($business->address); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Products -->
            <?php if($business->products->count() > 0): ?>
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Products</h3>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Product</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Quantity</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Unit Price</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Total</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php $__currentLoopData = $business->products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td class="px-6 py-4">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900"><?php echo e($product->name); ?></div>
                                                <?php if($product->sku): ?>
                                                    <div class="text-sm text-gray-500">SKU: <?php echo e($product->sku); ?></div>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-900"><?php echo e($product->pivot->quantity); ?></td>
                                        <td class="px-6 py-4 text-sm text-gray-900">
                                            <?php if($product->pivot->unit_price): ?>
                                                <?php echo e(number_format($product->pivot->unit_price, 2)); ?> SAR
                                            <?php else: ?>
                                                <?php echo e($product->formatted_price); ?>

                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-900">
                                            <?php
                                                $price = $product->pivot->unit_price ?? $product->price;
                                                $total = $price * $product->pivot->quantity;
                                            ?>
                                            <?php echo e(number_format($total, 2)); ?> SAR
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Notes -->
            <?php if($business->notes): ?>
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Notes</h3>
                    <p class="text-gray-900 whitespace-pre-wrap"><?php echo e($business->notes); ?></p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Status Card -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Status</h3>
                
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Business Status</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($business->status_badge); ?>">
                            <?php echo e(ucfirst($business->status)); ?>

                        </span>
                    </div>
                    
                    <?php if($business->lead_score): ?>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Lead Score</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($business->lead_score_badge); ?>">
                                <?php echo e($business->lead_score === 'lost' ? 'Lost' : $business->lead_score); ?>

                            </span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($business->lead_source): ?>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Lead Source</span>
                            <span class="text-gray-900 font-medium"><?php echo e($business->lead_source); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($business->estimated_value): ?>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Estimated Value</span>
                            <span class="text-gray-900 font-medium"><?php echo e(number_format($business->estimated_value, 2)); ?> SAR</span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($business->expected_close_date): ?>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Expected Close</span>
                            <span class="text-gray-900 font-medium"><?php echo e($business->expected_close_date->format('M d, Y')); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
                
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Invoices</span>
                        <span class="text-gray-900 font-medium"><?php echo e($business->invoices->count()); ?></span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Documents</span>
                        <span class="text-gray-900 font-medium"><?php echo e($business->documents->count()); ?></span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">WhatsApp Messages</span>
                        <span class="text-gray-900 font-medium"><?php echo e($business->whatsappTraffic->count()); ?></span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Created</span>
                        <span class="text-gray-900 font-medium"><?php echo e($business->created_at->format('M d, Y')); ?></span>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Actions</h3>
                
                <div class="space-y-2">
                    <a href="<?php echo e(route('businesses.edit', $business)); ?>" 
                       class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium text-center block">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Business
                    </a>
                    
                    <button class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium">
                        <i class="fas fa-file-invoice mr-2"></i>
                        Create Invoice
                    </button>
                    
                    <button class="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium">
                        <i class="fas fa-upload mr-2"></i>
                        Upload Document
                    </button>
                    
                    <form method="POST" action="<?php echo e(route('businesses.destroy', $business)); ?>" 
                          class="w-full" onsubmit="return confirm('Are you sure you want to delete this business?')">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium">
                            <i class="fas fa-trash mr-2"></i>
                            Delete Business
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale0f1cdd055772eb1d4a99981c240763e)): ?>
<?php $attributes = $__attributesOriginale0f1cdd055772eb1d4a99981c240763e; ?>
<?php unset($__attributesOriginale0f1cdd055772eb1d4a99981c240763e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale0f1cdd055772eb1d4a99981c240763e)): ?>
<?php $component = $__componentOriginale0f1cdd055772eb1d4a99981c240763e; ?>
<?php unset($__componentOriginale0f1cdd055772eb1d4a99981c240763e); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/Herd/admin/resources/views/businesses/show.blade.php ENDPATH**/ ?>