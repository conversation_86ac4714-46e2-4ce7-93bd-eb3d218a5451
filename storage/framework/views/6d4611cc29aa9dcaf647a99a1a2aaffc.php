<?php if (isset($component)) { $__componentOriginale0f1cdd055772eb1d4a99981c240763e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale0f1cdd055772eb1d4a99981c240763e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Businesses</h1>
                <p class="text-gray-600">Manage your business leads and clients</p>
            </div>
            <a href="<?php echo e(route('businesses.create')); ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                <i class="fas fa-plus mr-2"></i>
                Add Business
            </a>
        </div>
     <?php $__env->endSlot(); ?>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow mb-6 p-6">
        <form method="GET" action="<?php echo e(route('businesses.index')); ?>" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Search -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" name="search" value="<?php echo e(request('search')); ?>" 
                       placeholder="Search businesses..." 
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>

            <!-- Status Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select name="status" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Statuses</option>
                    <option value="lead" <?php echo e(request('status') === 'lead' ? 'selected' : ''); ?>>Lead</option>
                    <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                    <option value="closed" <?php echo e(request('status') === 'closed' ? 'selected' : ''); ?>>Closed</option>
                </select>
            </div>

            <!-- Lead Score Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Lead Score</label>
                <select name="lead_score" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Scores</option>
                    <option value="10" <?php echo e(request('lead_score') === '10' ? 'selected' : ''); ?>>10 - Low</option>
                    <option value="30" <?php echo e(request('lead_score') === '30' ? 'selected' : ''); ?>>30 - Medium</option>
                    <option value="70" <?php echo e(request('lead_score') === '70' ? 'selected' : ''); ?>>70 - High</option>
                    <option value="100" <?php echo e(request('lead_score') === '100' ? 'selected' : ''); ?>>100 - Hot</option>
                    <option value="lost" <?php echo e(request('lead_score') === 'lost' ? 'selected' : ''); ?>>Lost</option>
                </select>
            </div>

            <!-- Lead Source Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Lead Source</label>
                <select name="lead_source" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Sources</option>
                    <?php $__currentLoopData = $leadSources; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $source): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($source); ?>" <?php echo e(request('lead_source') === $source ? 'selected' : ''); ?>>
                            <?php echo e($source); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>

            <!-- Filter Buttons -->
            <div class="md:col-span-4 flex space-x-2">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium">
                    <i class="fas fa-search mr-2"></i>
                    Filter
                </button>
                <a href="<?php echo e(route('businesses.index')); ?>" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md font-medium">
                    <i class="fas fa-times mr-2"></i>
                    Clear
                </a>
            </div>
        </form>
    </div>

    <!-- Bulk Actions -->
    <div class="bg-white rounded-lg shadow mb-6 p-4" id="bulk-actions" style="display: none;">
        <form method="POST" action="<?php echo e(route('businesses.bulk-update')); ?>" id="bulk-form">
            <?php echo csrf_field(); ?>
            <div class="flex items-center space-x-4">
                <span class="text-sm font-medium text-gray-700">
                    <span id="selected-count">0</span> businesses selected
                </span>
                
                <select name="action" class="border border-gray-300 rounded-md px-3 py-2" required>
                    <option value="">Select Action</option>
                    <option value="update_status">Update Status</option>
                    <option value="update_lead_score">Update Lead Score</option>
                    <option value="update_lead_source">Update Lead Source</option>
                </select>

                <select name="status" class="border border-gray-300 rounded-md px-3 py-2" style="display: none;">
                    <option value="lead">Lead</option>
                    <option value="active">Active</option>
                    <option value="closed">Closed</option>
                </select>

                <select name="lead_score" class="border border-gray-300 rounded-md px-3 py-2" style="display: none;">
                    <option value="10">10 - Low</option>
                    <option value="30">30 - Medium</option>
                    <option value="70">70 - High</option>
                    <option value="100">100 - Hot</option>
                    <option value="lost">Lost</option>
                </select>

                <input type="text" name="lead_source" placeholder="Lead Source" 
                       class="border border-gray-300 rounded-md px-3 py-2" style="display: none;">

                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium">
                    Apply
                </button>
            </div>
        </form>
    </div>

    <!-- Businesses Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left">
                            <input type="checkbox" id="select-all" class="rounded border-gray-300">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <a href="<?php echo e(request()->fullUrlWithQuery(['sort_by' => 'name', 'sort_direction' => request('sort_direction') === 'asc' ? 'desc' : 'asc'])); ?>" 
                               class="flex items-center hover:text-gray-700">
                                Business
                                <i class="fas fa-sort ml-1"></i>
                            </a>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lead Score</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <a href="<?php echo e(request()->fullUrlWithQuery(['sort_by' => 'estimated_value', 'sort_direction' => request('sort_direction') === 'asc' ? 'desc' : 'asc'])); ?>" 
                               class="flex items-center hover:text-gray-700">
                                Value
                                <i class="fas fa-sort ml-1"></i>
                            </a>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php $__empty_1 = true; $__currentLoopData = $businesses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $business): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <input type="checkbox" name="business_ids[]" value="<?php echo e($business->id); ?>" 
                                       class="business-checkbox rounded border-gray-300">
                            </td>
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($business->name); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e($business->industry); ?></div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm text-gray-900"><?php echo e($business->contact_person); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e($business->email); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e($business->phone); ?></div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($business->status_badge); ?>">
                                    <?php echo e(ucfirst($business->status)); ?>

                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <?php if($business->lead_score): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($business->lead_score_badge); ?>">
                                        <?php echo e($business->lead_score === 'lost' ? 'Lost' : $business->lead_score); ?>

                                    </span>
                                <?php else: ?>
                                    <span class="text-gray-400">-</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                <?php if($business->estimated_value): ?>
                                    <?php echo e(number_format($business->estimated_value, 2)); ?> SAR
                                <?php else: ?>
                                    <span class="text-gray-400">-</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 text-sm font-medium space-x-2">
                                <a href="<?php echo e(route('businesses.show', $business)); ?>" 
                                   class="text-blue-600 hover:text-blue-900">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?php echo e(route('businesses.edit', $business)); ?>" 
                                   class="text-indigo-600 hover:text-indigo-900">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form method="POST" action="<?php echo e(route('businesses.destroy', $business)); ?>" 
                                      class="inline" onsubmit="return confirm('Are you sure?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="text-red-600 hover:text-red-900">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center text-gray-500">
                                <i class="fas fa-building text-4xl mb-4"></i>
                                <p class="text-lg">No businesses found</p>
                                <p class="text-sm">Get started by adding your first business</p>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if($businesses->hasPages()): ?>
            <div class="px-6 py-4 border-t border-gray-200">
                <?php echo e($businesses->links()); ?>

            </div>
        <?php endif; ?>
    </div>

    <script>
        // Bulk selection functionality
        document.getElementById('select-all').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.business-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });

        document.querySelectorAll('.business-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', updateBulkActions);
        });

        function updateBulkActions() {
            const checkedBoxes = document.querySelectorAll('.business-checkbox:checked');
            const bulkActions = document.getElementById('bulk-actions');
            const selectedCount = document.getElementById('selected-count');
            
            if (checkedBoxes.length > 0) {
                bulkActions.style.display = 'block';
                selectedCount.textContent = checkedBoxes.length;
                
                // Add checked IDs to form
                const form = document.getElementById('bulk-form');
                const existingInputs = form.querySelectorAll('input[name="business_ids[]"]');
                existingInputs.forEach(input => input.remove());
                
                checkedBoxes.forEach(checkbox => {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'business_ids[]';
                    input.value = checkbox.value;
                    form.appendChild(input);
                });
            } else {
                bulkActions.style.display = 'none';
            }
        }

        // Show/hide action-specific fields
        document.querySelector('select[name="action"]').addEventListener('change', function() {
            const statusSelect = document.querySelector('select[name="status"]');
            const leadScoreSelect = document.querySelector('select[name="lead_score"]');
            const leadSourceInput = document.querySelector('input[name="lead_source"]');
            
            // Hide all
            statusSelect.style.display = 'none';
            leadScoreSelect.style.display = 'none';
            leadSourceInput.style.display = 'none';
            
            // Show relevant field
            switch(this.value) {
                case 'update_status':
                    statusSelect.style.display = 'block';
                    statusSelect.required = true;
                    break;
                case 'update_lead_score':
                    leadScoreSelect.style.display = 'block';
                    leadScoreSelect.required = true;
                    break;
                case 'update_lead_source':
                    leadSourceInput.style.display = 'block';
                    leadSourceInput.required = true;
                    break;
            }
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale0f1cdd055772eb1d4a99981c240763e)): ?>
<?php $attributes = $__attributesOriginale0f1cdd055772eb1d4a99981c240763e; ?>
<?php unset($__attributesOriginale0f1cdd055772eb1d4a99981c240763e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale0f1cdd055772eb1d4a99981c240763e)): ?>
<?php $component = $__componentOriginale0f1cdd055772eb1d4a99981c240763e; ?>
<?php unset($__componentOriginale0f1cdd055772eb1d4a99981c240763e); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/Herd/admin/resources/views/businesses/index.blade.php ENDPATH**/ ?>