<?php if (isset($component)) { $__componentOriginale0f1cdd055772eb1d4a99981c240763e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale0f1cdd055772eb1d4a99981c240763e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h1 class="text-2xl font-bold text-gray-900">360Dialog Accounts</h1>
        <p class="text-gray-600">WhatsApp Business API account management</p>
     <?php $__env->endSlot(); ?>

    <div id="loading-state" class="bg-white rounded-lg shadow p-8 text-center">
        <i class="fas fa-spinner fa-spin text-4xl text-gray-400 mb-4"></i>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Loading Account Information</h3>
        <p class="text-gray-600">Checking 360Dialog configuration...</p>
    </div>

    <div id="not-configured" class="bg-white rounded-lg shadow p-8 text-center hidden">
        <i class="fab fa-whatsapp text-6xl text-green-500 mb-4"></i>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">WhatsApp Business Integration</h3>
        <p class="text-gray-600 mb-6">Connect your WhatsApp Business account through 360Dialog for automated messaging.</p>
        <a href="<?php echo e(route('settings.360dialog')); ?>" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium">
            Configure Integration
        </a>
    </div>

    <div id="configured" class="hidden">
        <!-- User Profile Section -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">User Profile</h3>
            <div id="user-profile-content" class="space-y-4">
                <!-- User profile data will be loaded here -->
            </div>
        </div>

        <!-- Account Details Section -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Account Details</h3>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <label for="results-per-page" class="text-sm font-medium text-gray-700">Results per page:</label>
                        <select id="results-per-page" class="border-gray-300 rounded-md shadow-sm text-sm" onchange="updateResultsLimit()">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50" selected>50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                    <button onclick="refreshAccountData()" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                        <i class="fas fa-refresh mr-1"></i> Refresh
                    </button>
                </div>
            </div>

            <!-- Pagination Info -->
            <div id="pagination-info" class="mb-4 text-sm text-gray-600 hidden">
                <!-- Pagination information will be displayed here -->
            </div>

            <div id="account-details-content" class="space-y-4">
                <!-- Account details data will be loaded here -->
            </div>

            <!-- Pagination Controls -->
            <div id="pagination-controls" class="mt-6 flex justify-between items-center hidden">
                <button id="prev-page" onclick="loadPreviousPage()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                    <i class="fas fa-chevron-left mr-1"></i> Previous
                </button>

                <div id="page-info" class="text-sm text-gray-600">
                    <!-- Page information will be displayed here -->
                </div>

                <button id="next-page" onclick="loadNextPage()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                    Next <i class="fas fa-chevron-right ml-1"></i>
                </button>
            </div>
        </div>
    </div>

    <div id="error-state" class="bg-white rounded-lg shadow p-8 text-center hidden">
        <i class="fas fa-exclamation-triangle text-6xl text-red-500 mb-4"></i>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Connection Error</h3>
        <p class="text-gray-600 mb-6" id="error-message">Unable to connect to 360Dialog API.</p>
        <button onclick="loadAccountData()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium mr-4">
            Retry
        </button>
        <a href="<?php echo e(route('settings.360dialog')); ?>" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium">
            Check Configuration
        </a>
    </div>

    <script>
        // Pagination state
        let currentPage = 0;
        let currentLimit = 50;
        let totalCount = 0;
        let allChannels = []; // Store all channels locally
        let accountData = null;

        async function loadAccountData(page = 0, limit = null, forceRefresh = false) {
            try {
                // Ensure we have a valid limit
                if (limit === null) {
                    const selectElement = document.getElementById('results-per-page');
                    limit = selectElement ? parseInt(selectElement.value) : 50;
                }

                // Ensure limit is a valid number
                if (isNaN(limit) || limit <= 0) {
                    limit = 50;
                }

                currentPage = page;
                currentLimit = limit;

                console.log('Loading account data with page:', page, 'limit:', limit);

                // Show loading state
                document.getElementById('loading-state').classList.remove('hidden');
                document.getElementById('not-configured').classList.add('hidden');
                document.getElementById('configured').classList.add('hidden');
                document.getElementById('error-state').classList.add('hidden');

                // Check authentication status first
                const authResponse = await fetch('/api/360dialog/auth/status', {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const authData = await authResponse.json();

                if (!authData.authenticated) {
                    // Not configured
                    document.getElementById('loading-state').classList.add('hidden');
                    document.getElementById('not-configured').classList.remove('hidden');
                    return;
                }

                // Load user profile
                const profileResponse = await fetch('/api/360dialog/user-profile', {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const profileData = await profileResponse.json();

                if (profileData.success) {
                    displayUserProfile(profileData.data);
                }

                // Load all account details if not cached or force refresh
                if (allChannels.length === 0 || forceRefresh) {
                    await loadAllChannels();
                }

                // Display current page of results
                displayCurrentPage();

                // Show configured state
                document.getElementById('loading-state').classList.add('hidden');
                document.getElementById('configured').classList.remove('hidden');

            } catch (error) {
                console.error('Error loading account data:', error);
                document.getElementById('loading-state').classList.add('hidden');
                document.getElementById('error-state').classList.remove('hidden');
                document.getElementById('error-message').textContent = 'Failed to load account data. Please try again.';
            }
        }

        async function loadAllChannels() {
            try {
                console.log('Loading all channels from API...');

                // Start with a large limit to get all results
                // We'll fetch in batches if needed
                let offset = 0;
                let limit = 1000; // Large batch size
                let hasMore = true;
                allChannels = [];

                while (hasMore) {
                    const response = await fetch(`/api/360dialog/account-details?offset=${offset}&limit=${limit}`, {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    });

                    const data = await response.json();

                    if (data.success) {
                        const channels = data.data.partner_channels || [];
                        allChannels = allChannels.concat(channels);

                        // Update total count from API response
                        const apiTotalCount = data.data.count || 0;

                        // Check if we have more data to fetch
                        if (channels.length < limit || allChannels.length >= apiTotalCount) {
                            hasMore = false;
                        } else {
                            offset += limit;
                        }

                        console.log(`Loaded ${allChannels.length} of ${totalCount} channels`);
                    } else {
                        hasMore = false;
                        throw new Error(data.message || 'Failed to load channels');
                    }
                }

                // Set totalCount to actual loaded channels
                totalCount = allChannels.length;
                console.log(`Successfully loaded all ${allChannels.length} channels`);
            } catch (error) {
                console.error('Error loading all channels:', error);
                throw error;
            }
        }

        function displayCurrentPage() {
            const startIndex = currentPage * currentLimit;
            const endIndex = Math.min(startIndex + currentLimit, allChannels.length);
            const pageChannels = allChannels.slice(startIndex, endIndex);

            // Create a mock data structure similar to the API response
            const pageData = {
                partner_channels: pageChannels,
                count: totalCount
            };

            displayAccountDetails(pageData);
            updatePaginationControls();
        }

        function displayUserProfile(data) {
            const content = document.getElementById('user-profile-content');
            content.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Email</label>
                        <p class="text-gray-900">${data.email || 'N/A'}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Name</label>
                        <p class="text-gray-900">${data.name || 'N/A'}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Active Partner</label>
                        <p class="text-gray-900">${data.active_partner || 'N/A'}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Organizations</label>
                        <p class="text-gray-900">${data.organizations ? data.organizations.length : 0} organization(s)</p>
                    </div>
                </div>
            `;
        }

        function displayAccountDetails(data) {
            const content = document.getElementById('account-details-content');
            const channels = data.partner_channels || [];

            content.innerHTML = `
                <div class="space-y-4">
                    ${channels.map(channel => `
                        <div class="border rounded-lg p-4" data-channel-id="${channel.id}">
                            <div class="flex justify-between items-start mb-2">
                                <h4 class="font-semibold text-gray-900">${channel.client?.name || 'Unknown Client'}</h4>
                                <div class="flex items-center space-x-2">
                                    <span class="px-2 py-1 text-xs rounded-full ${channel.status === 'ready' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">${channel.status}</span>
                                    <button onclick="viewTrafficForChannel('${channel.id}', '${channel.client?.name || 'Unknown'}')" class="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs">
                                        <i class="fas fa-chart-line mr-1"></i> View Traffic
                                    </button>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-600">Channel ID:</span>
                                    <span class="text-gray-900 font-mono text-xs">${channel.id}</span>
                                </div>
                                <div>
                                    <span class="text-gray-600">Phone:</span>
                                    <span class="text-gray-900">${channel.setup_info?.phone_number || 'N/A'}</span>
                                </div>
                                <div>
                                    <span class="text-gray-600">Quality:</span>
                                    <span class="text-gray-900">${channel.current_quality_rating || 'N/A'}</span>
                                </div>
                                <div>
                                    <span class="text-gray-600">Limit:</span>
                                    <span class="text-gray-900">${channel.current_limit || 'N/A'}</span>
                                </div>
                                <div>
                                    <span class="text-gray-600">Balance:</span>
                                    <span class="text-gray-900">${channel.current_balance || 'N/A'}</span>
                                </div>
                                <div>
                                    <span class="text-gray-600">Project:</span>
                                    <span class="text-gray-900">${channel.project?.name || 'N/A'}</span>
                                </div>
                            </div>
                            ${channel.client ? `
                                <div class="mt-3 pt-3 border-t border-gray-200">
                                    <h5 class="text-sm font-medium text-gray-700 mb-2">Business Details:</h5>
                                    <div class="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <span class="text-gray-600">Client ID:</span>
                                            <span class="text-gray-900 font-mono text-xs">${channel.client.id}</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-600">Created:</span>
                                            <span class="text-gray-900">${new Date(channel.client.created_at).toLocaleDateString()}</span>
                                        </div>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    `).join('')}
                </div>
            `;
        }

        function updatePaginationControls() {
            const paginationInfo = document.getElementById('pagination-info');
            const paginationControls = document.getElementById('pagination-controls');
            const pageInfo = document.getElementById('page-info');
            const prevButton = document.getElementById('prev-page');
            const nextButton = document.getElementById('next-page');

            // Ensure we have valid values
            const validTotalCount = totalCount || 0;
            const validCurrentPage = currentPage || 0;
            const validCurrentLimit = currentLimit || 50;

            if (validTotalCount > 0) {
                const startItem = (validCurrentPage * validCurrentLimit) + 1;
                const endItem = Math.min((validCurrentPage + 1) * validCurrentLimit, validTotalCount);
                const totalPages = Math.ceil(validTotalCount / validCurrentLimit);

                paginationInfo.innerHTML = `Showing ${startItem}-${endItem} of ${validTotalCount} channels (All ${validTotalCount} channels loaded)`;
                paginationInfo.classList.remove('hidden');

                pageInfo.innerHTML = `Page ${validCurrentPage + 1} of ${totalPages}`;

                prevButton.disabled = validCurrentPage === 0;
                nextButton.disabled = (validCurrentPage + 1) * validCurrentLimit >= validTotalCount;

                paginationControls.classList.remove('hidden');
            } else {
                paginationInfo.innerHTML = 'Loading channels...';
                paginationInfo.classList.remove('hidden');
                paginationControls.classList.add('hidden');
            }
        }

        function loadPreviousPage() {
            if (currentPage > 0) {
                loadAccountData(currentPage - 1, currentLimit);
            }
        }

        function loadNextPage() {
            if ((currentPage + 1) * currentLimit < totalCount) {
                loadAccountData(currentPage + 1, currentLimit);
            }
        }

        function updateResultsLimit() {
            const newLimit = parseInt(document.getElementById('results-per-page').value);
            loadAccountData(0, newLimit); // Reset to first page with new limit
        }

        function refreshAccountData() {
            loadAccountData(currentPage, currentLimit, true); // Force refresh
        }

        function viewTrafficForChannel(channelId, clientName) {
            // Store channel info in localStorage for the traffic page
            localStorage.setItem('selectedChannel', JSON.stringify({
                id: channelId,
                name: clientName
            }));

            // Navigate to traffic page
            window.location.href = '/360dialog/traffic';
        }

        // Load data when page loads
        document.addEventListener('DOMContentLoaded', loadAccountData);
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale0f1cdd055772eb1d4a99981c240763e)): ?>
<?php $attributes = $__attributesOriginale0f1cdd055772eb1d4a99981c240763e; ?>
<?php unset($__attributesOriginale0f1cdd055772eb1d4a99981c240763e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale0f1cdd055772eb1d4a99981c240763e)): ?>
<?php $component = $__componentOriginale0f1cdd055772eb1d4a99981c240763e; ?>
<?php unset($__componentOriginale0f1cdd055772eb1d4a99981c240763e); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/Herd/admin/resources/views/360dialog/accounts.blade.php ENDPATH**/ ?>