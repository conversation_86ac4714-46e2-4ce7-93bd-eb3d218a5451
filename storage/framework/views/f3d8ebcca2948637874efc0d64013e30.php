<?php if (isset($component)) { $__componentOriginale0f1cdd055772eb1d4a99981c240763e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale0f1cdd055772eb1d4a99981c240763e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p class="text-gray-600">Welcome back! Here's what's happening with your business.</p>
     <?php $__env->endSlot(); ?>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Businesses -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-building text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Businesses</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($totalBusinesses); ?></p>
                </div>
            </div>
        </div>

        <!-- Lead Businesses -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <i class="fas fa-user-plus text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Leads</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($leadBusinesses); ?></p>
                </div>
            </div>
        </div>

        <!-- Active Businesses -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-check-circle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($activeBusinesses); ?></p>
                </div>
            </div>
        </div>

        <!-- Revenue -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                    <i class="fas fa-dollar-sign text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e(number_format($totalRevenue, 2)); ?> SAR</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Lead Score Distribution -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Lead Score Distribution</h3>
            <div class="space-y-3">
                <?php $__currentLoopData = ['10' => 'Low (10)', '30' => 'Medium (30)', '70' => 'High (70)', '100' => 'Hot (100)', 'lost' => 'Lost']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $score => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600"><?php echo e($label); ?></span>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                                <?php
                                    $count = $leadScoreStats[$score] ?? 0;
                                    $percentage = $leadBusinesses > 0 ? ($count / $leadBusinesses) * 100 : 0;
                                ?>
                                <div class="bg-blue-600 h-2 rounded-full" style="width: <?php echo e($percentage); ?>%"></div>
                            </div>
                            <span class="text-sm font-medium text-gray-900"><?php echo e($count); ?></span>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>

        <!-- Recent Businesses -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Businesses</h3>
            <div class="space-y-3">
                <?php $__empty_1 = true; $__currentLoopData = $recentBusinesses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $business): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <p class="font-medium text-gray-900"><?php echo e($business->name); ?></p>
                            <p class="text-sm text-gray-600"><?php echo e($business->contact_person); ?></p>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($business->status_badge); ?>">
                                <?php echo e(ucfirst($business->status)); ?>

                            </span>
                            <?php if($business->lead_score): ?>
                                <p class="text-xs text-gray-500 mt-1">Score: <?php echo e($business->lead_score); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <p class="text-gray-500 text-center py-4">No businesses found</p>
                <?php endif; ?>
            </div>
            <div class="mt-4">
                <a href="<?php echo e(route('businesses.index')); ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    View all businesses →
                </a>
            </div>
        </div>
    </div>

    <!-- Additional Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Invoice Statistics -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Invoices</h3>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-gray-600">Total</span>
                    <span class="font-medium"><?php echo e($totalInvoices); ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Paid</span>
                    <span class="font-medium text-green-600"><?php echo e($paidInvoices); ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Pending</span>
                    <span class="font-medium text-yellow-600"><?php echo e($pendingInvoices); ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Overdue</span>
                    <span class="font-medium text-red-600"><?php echo e($overdueInvoices); ?></span>
                </div>
            </div>
        </div>

        <!-- WhatsApp Traffic -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">WhatsApp Traffic</h3>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-gray-600">Total Messages</span>
                    <span class="font-medium"><?php echo e($totalMessages); ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Today</span>
                    <span class="font-medium"><?php echo e($todayMessages); ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Inbound</span>
                    <span class="font-medium text-blue-600"><?php echo e($inboundMessages); ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Outbound</span>
                    <span class="font-medium text-green-600"><?php echo e($outboundMessages); ?></span>
                </div>
            </div>
        </div>

        <!-- Lead Sources -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Lead Sources</h3>
            <div class="space-y-2">
                <?php $__empty_1 = true; $__currentLoopData = $leadSources; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $source => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="flex justify-between">
                        <span class="text-gray-600"><?php echo e($source); ?></span>
                        <span class="font-medium"><?php echo e($count); ?></span>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <p class="text-gray-500 text-center py-2">No lead sources</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale0f1cdd055772eb1d4a99981c240763e)): ?>
<?php $attributes = $__attributesOriginale0f1cdd055772eb1d4a99981c240763e; ?>
<?php unset($__attributesOriginale0f1cdd055772eb1d4a99981c240763e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale0f1cdd055772eb1d4a99981c240763e)): ?>
<?php $component = $__componentOriginale0f1cdd055772eb1d4a99981c240763e; ?>
<?php unset($__componentOriginale0f1cdd055772eb1d4a99981c240763e); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/Herd/admin/resources/views/dashboard.blade.php ENDPATH**/ ?>