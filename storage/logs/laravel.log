[2025-06-29 01:16:39] testing.ERROR: SQLSTATE[HY000]: General error: 1 no such function: MONTH (Connection: sqlite, SQL: select MONTH(created_at) as month, COUNT(*) as count from "businesses" where strftime('%Y', "created_at") = cast(2025 as text) group by "month" order by "month" asc) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such function: MONTH (Connection: sqlite, SQL: select MONTH(created_at) as month, COUNT(*) as count from \"businesses\" where strftime('%Y', \"created_at\") = cast(2025 as text) group by \"month\" order by \"month\" asc) at /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select MONTH(cr...', Array, Object(Closure))
#1 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select MONTH(cr...', Array, Object(Closure))
#2 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select MONTH(cr...', Array, true)
#3 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3389): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3385): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1052): Illuminate\\Database\\Query\\Builder->pluck('count', 'month')
#7 /Users/<USER>/Herd/admin/business-management-platform/app/Http/Controllers/DashboardController.php(63): Illuminate\\Database\\Eloquent\\Builder->pluck('count', 'month')
#8 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\DashboardController->index()
#9 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), 'index')
#10 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#11 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#12 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#25 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#34 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#36 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(368): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/dashboard', Array, Array, Array, Array)
#60 /Users/<USER>/Herd/admin/business-management-platform/tests/Feature/BusinessTest.php(24): Illuminate\\Foundation\\Testing\\TestCase->get('/dashboard')
#61 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\BusinessTest->test_authenticated_user_can_access_dashboard()
#62 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#63 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#64 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\BusinessTest))
#65 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#66 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#67 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#68 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#69 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#70 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#71 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such function: MONTH at /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Connection.php:404)
[stacktrace]
#0 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): PDO->prepare('select MONTH(cr...')
#1 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select MONTH(cr...', Array)
#2 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select MONTH(cr...', Array, Object(Closure))
#3 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select MONTH(cr...', Array, Object(Closure))
#4 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select MONTH(cr...', Array, true)
#5 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3389): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3385): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1052): Illuminate\\Database\\Query\\Builder->pluck('count', 'month')
#9 /Users/<USER>/Herd/admin/business-management-platform/app/Http/Controllers/DashboardController.php(63): Illuminate\\Database\\Eloquent\\Builder->pluck('count', 'month')
#10 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\DashboardController->index()
#11 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), 'index')
#12 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#13 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#14 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(368): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/dashboard', Array, Array, Array, Array)
#62 /Users/<USER>/Herd/admin/business-management-platform/tests/Feature/BusinessTest.php(24): Illuminate\\Foundation\\Testing\\TestCase->get('/dashboard')
#63 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\BusinessTest->test_authenticated_user_can_access_dashboard()
#64 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#65 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#66 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\BusinessTest))
#67 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#68 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#69 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#70 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#71 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#72 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#73 {main}
"} 
[2025-06-29 22:42:53] local.ERROR: Target class [App\Http\Controllers\TaqnyatAuthController] does not exist. {"userId":1,"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\TaqnyatAuthController] does not exist. at /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php:1019)
[stacktrace]
#0 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(266): Illuminate\\Routing\\Route->getController()
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Herd/admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#55 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\TaqnyatAuthController\" does not exist at /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php:1017)
[stacktrace]
#0 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(1017): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(266): Illuminate\\Routing\\Route->getController()
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 /Users/<USER>/Herd/admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#55 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#56 {main}
"} 
[2025-06-29 22:42:58] local.ERROR: Target class [App\Http\Controllers\TaqnyatAuthController] does not exist. {"userId":1,"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\TaqnyatAuthController] does not exist. at /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php:1019)
[stacktrace]
#0 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(266): Illuminate\\Routing\\Route->getController()
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Herd/admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#55 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\TaqnyatAuthController\" does not exist at /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php:1017)
[stacktrace]
#0 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(1017): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(266): Illuminate\\Routing\\Route->getController()
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 /Users/<USER>/Herd/admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#55 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#56 {main}
"} 
[2025-06-29 22:43:24] local.ERROR: Target class [App\Http\Controllers\Dialog360SettingsController] does not exist. {"userId":1,"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\Dialog360SettingsController] does not exist. at /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php:1019)
[stacktrace]
#0 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(266): Illuminate\\Routing\\Route->getController()
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Herd/admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#55 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Dialog360SettingsController\" does not exist at /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php:1017)
[stacktrace]
#0 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(1017): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(266): Illuminate\\Routing\\Route->getController()
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 /Users/<USER>/Herd/admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#55 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#56 {main}
"} 
[2025-06-29 23:07:45] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(38): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:07:45] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:07:47] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c741cd876","user_id":1} 
[2025-06-29 23:07:47] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:07:48] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c7436df00","user_id":1} 
[2025-06-29 23:07:48] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861c7436df00","user_id":1} 
[2025-06-29 23:07:50] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861c7436df00","phone_number":"+************","user_id":1} 
[2025-06-29 23:07:50] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":36} 
[2025-06-29 23:07:50] testing.WARNING: 360Dialog token validation failed {"user_id":1,"token_length":36} 
[2025-06-29 23:07:50] testing.ERROR: Invalid Bearer token. Please check your token and try again. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): Invalid Bearer token. Please check your token and try again. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:84)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/Dialog360SettingsController.php(67): App\\Exceptions\\ApiIntegrationException::invalidToken('360dialog', 'Invalid Bearer ...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\Dialog360SettingsController->updateToken(Object(App\\Http\\Requests\\Dialog360TokenRequest))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dialog360SettingsController), 'updateToken')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/360dialog/...', Array, Array, Array, Array, '{\"bearer_token\"...')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/360dialog/...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(175): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/360dialog/...', Array)
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_360dialog_token_update_with_valid_token()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:07:50] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '{\"phone_number\"...')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(237): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...', Array)
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_phone_number_formatting_in_requests()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:08:59] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(38): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:08:59] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:09:01] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c78bae908","user_id":1} 
[2025-06-29 23:09:01] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:09:02] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c78d15bd2","user_id":1} 
[2025-06-29 23:09:02] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861c78d15bd2","user_id":1} 
[2025-06-29 23:09:03] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861c78d15bd2","phone_number":"+************","user_id":1} 
[2025-06-29 23:09:03] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":36} 
[2025-06-29 23:09:03] testing.WARNING: 360Dialog token validation failed {"user_id":1,"token_length":36} 
[2025-06-29 23:09:03] testing.ERROR: Invalid Bearer token. Please check your token and try again. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): Invalid Bearer token. Please check your token and try again. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:84)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/Dialog360SettingsController.php(67): App\\Exceptions\\ApiIntegrationException::invalidToken('360dialog', 'Invalid Bearer ...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\Dialog360SettingsController->updateToken(Object(App\\Http\\Requests\\Dialog360TokenRequest))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dialog360SettingsController), 'updateToken')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/360dialog/...', Array, Array, Array, Array, '{\"bearer_token\"...')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/360dialog/...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(175): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/360dialog/...', Array)
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_360dialog_token_update_with_valid_token()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:09:03] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:09:05] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c78fd68b4","user_id":1} 
[2025-06-29 23:09:34] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(38): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:09:34] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:09:36] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c7aee6de4","user_id":1} 
[2025-06-29 23:09:36] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:09:37] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c7b0514af","user_id":1} 
[2025-06-29 23:09:37] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861c7b0514af","user_id":1} 
[2025-06-29 23:09:38] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861c7b0514af","phone_number":"+************","user_id":1} 
[2025-06-29 23:09:38] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":36} 
[2025-06-29 23:09:38] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:09:38] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:09:40] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c7b2e3bc0","user_id":1} 
[2025-06-29 23:11:59] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:12:01] local.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c83fd64c0","user_id":1} 
[2025-06-29 23:15:47] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(42): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:15:48] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:15:49] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c92402d50","user_id":1} 
[2025-06-29 23:15:49] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:15:50] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c925684ed","user_id":1} 
[2025-06-29 23:15:50] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861c925684ed","user_id":1} 
[2025-06-29 23:15:51] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861c925684ed","phone_number":"+************","user_id":1} 
[2025-06-29 23:15:51] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":726} 
[2025-06-29 23:15:51] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":726} 
[2025-06-29 23:15:51] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:15:52] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:15:53] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c9280b1dd","user_id":1} 
[2025-06-29 23:15:53] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":726} 
[2025-06-29 23:15:53] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":726} 
[2025-06-29 23:15:53] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:15:53] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":726} 
[2025-06-29 23:15:53] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":726} 
[2025-06-29 23:15:53] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:16:17] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(42): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:16:17] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:16:19] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c941c6d72","user_id":1} 
[2025-06-29 23:16:19] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:16:20] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c9430fccb","user_id":1} 
[2025-06-29 23:16:20] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861c9430fccb","user_id":1} 
[2025-06-29 23:16:21] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861c9430fccb","phone_number":"+************","user_id":1} 
[2025-06-29 23:16:21] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":726} 
[2025-06-29 23:16:21] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":726} 
[2025-06-29 23:16:21] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:16:21] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:16:22] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c945ba8d8","user_id":1} 
[2025-06-29 23:16:23] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":726} 
[2025-06-29 23:16:23] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":726} 
[2025-06-29 23:16:23] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:16:23] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":726} 
[2025-06-29 23:16:23] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":726} 
[2025-06-29 23:16:23] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:17:27] local.INFO: 360Dialog token update initiated {"user_id":1,"token_length":500} 
[2025-06-29 23:17:28] local.WARNING: 360Dialog token validation failed {"user_id":1,"token_length":500} 
[2025-06-29 23:17:28] local.ERROR: Invalid Bearer token. Please check your token and try again. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): Invalid Bearer token. Please check your token and try again. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:84)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/Dialog360SettingsController.php(67): App\\Exceptions\\ApiIntegrationException::invalidToken('360dialog', 'Invalid Bearer ...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\Dialog360SettingsController->updateToken(Object(App\\Http\\Requests\\Dialog360TokenRequest))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dialog360SettingsController), 'updateToken')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#51 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#52 {main}
"} 
[2025-06-29 23:22:20] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(43): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:22:20] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:22:22] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861caaccfbbd","user_id":1} 
[2025-06-29 23:22:22] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:22:23] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861caae3faad","user_id":1} 
[2025-06-29 23:22:23] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861caae3faad","user_id":1} 
[2025-06-29 23:22:24] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861caae3faad","phone_number":"+************","user_id":1} 
[2025-06-29 23:22:24] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:22:24] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:24] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:24] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:22:24] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:24] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:24] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:22:24] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:24] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:24] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:22:24] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:22:24] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:22:24] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:22:26] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861cab0dc8d5","user_id":1} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:49] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(43): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:31:49] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:31:50] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861cce51ddf0","user_id":1} 
[2025-06-29 23:31:50] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:31:51] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861cce67387b","user_id":1} 
[2025-06-29 23:31:51] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861cce67387b","user_id":1} 
[2025-06-29 23:31:53] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861cce67387b","phone_number":"+************","user_id":1} 
[2025-06-29 23:31:53] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:31:53] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:53] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:53] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:53] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:53] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:53] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:53] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:53] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:53] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:53] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:31:53] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:31:53] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:31:54] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861cce91fb6c","user_id":1} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:31] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(43): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:32:31] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:32:33] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861cd0fd4540","user_id":1} 
[2025-06-29 23:32:33] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:32:34] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861cd114ff4c","user_id":1} 
[2025-06-29 23:32:34] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861cd114ff4c","user_id":1} 
[2025-06-29 23:32:35] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861cd114ff4c","phone_number":"+************","user_id":1} 
[2025-06-29 23:32:35] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:32:35] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:35] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:35] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:35] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:35] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:35] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:35] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:35] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:35] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:35] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:32:35] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:32:35] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:32:37] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861cd13f2b63","user_id":1} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:34:27] local.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:34:27] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:34:27] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:34:27] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:34:27] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:34:27] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:34:27] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:34:27] local.WARNING: 360Dialog token validation failed {"user_id":1,"token_length":1328} 
[2025-06-29 23:34:27] local.ERROR: Invalid Bearer token. Please check your token and try again. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): Invalid Bearer token. Please check your token and try again. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:84)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/Dialog360SettingsController.php(67): App\\Exceptions\\ApiIntegrationException::invalidToken('360dialog', 'Invalid Bearer ...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\Dialog360SettingsController->updateToken(Object(App\\Http\\Requests\\Dialog360TokenRequest))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dialog360SettingsController), 'updateToken')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#51 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#52 {main}
"} 
[2025-06-29 23:40:04] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:40:04] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:40:04] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:40:04] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:40:04] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:40:04] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:41:37] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:41:37] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:41:37] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:41:37] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:41:37] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:41:37] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:41:37] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:41:37] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:41:37] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:41:37] local.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:45:26] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d016b1dc5","username":"a.younis"} 
[2025-06-29 23:45:27] local.INFO: Taqnyat OTP request response {"status":200,"body":"otp:تم ارسال رمز التحقق لرقم الجوال 7928********","headers":{"Date":["Sun, 29 Jun 2025 23:45:27 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=mkanbvfnfha2tv9qg2fp045sv5; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Sun, 29 Jun 2025 23:45:27 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["74"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-29 23:45:27] local.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861d016b1dc5","otp":"123456","username":"a.younis","attempts":1} 
[2025-06-29 23:45:27] local.INFO: Taqnyat OTP verification response {"status":200,"body":"otp:رمز التحقق غير صحيح","headers":{"Date":["Sun, 29 Jun 2025 23:45:27 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=ovmbv3fv4rj70eu3ts1453vmuv; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Sun, 29 Jun 2025 23:45:27 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["39"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-29 23:46:10] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d04239cc1","username":"a.younis"} 
[2025-06-29 23:46:10] local.INFO: Taqnyat OTP request response {"status":200,"body":"otp:تم ارسال رمز التحقق لرقم الجوال 7928********","headers":{"Date":["Sun, 29 Jun 2025 23:46:10 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=s72icouufjp6m5i0tbm67s05fs; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Sun, 29 Jun 2025 23:46:10 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["74"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-29 23:46:10] local.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861d04239cc1","otp":"123456","username":"a.younis","attempts":1} 
[2025-06-29 23:46:11] local.INFO: Taqnyat OTP verification response {"status":200,"body":"otp:رمز التحقق غير صحيح","headers":{"Date":["Sun, 29 Jun 2025 23:46:11 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=l2dd3hcier33gfiv1ej8n00a72; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Sun, 29 Jun 2025 23:46:11 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["39"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-29 23:46:52] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d06c367c7","username":"a.younis"} 
[2025-06-29 23:46:52] local.INFO: Taqnyat OTP request response {"status":200,"body":"otp:لقد تم ارسال رمز التحقق سابقا يمكن المحاوله بعد  18 ثانيه","headers":{"Date":["Sun, 29 Jun 2025 23:46:52 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=fcs8bfa9hb2e9d9tppfg4kab2n; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Sun, 29 Jun 2025 23:46:52 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["105"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-29 23:46:52] local.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861d06c367c7","otp":"123456","username":"a.younis","attempts":1} 
[2025-06-29 23:46:53] local.INFO: Taqnyat OTP verification response {"status":200,"body":"otp:رمز التحقق غير صحيح","headers":{"Date":["Sun, 29 Jun 2025 23:46:53 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=agsfaej5o73vfdg50b7c3ln344; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Sun, 29 Jun 2025 23:46:53 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["39"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-29 23:46:53] local.INFO: Taqnyat OTP verification successful (Demo Mode - Arabic Response) {"session_id":"taqnyat_otp_6861d06c367c7","otp":"123456","access_token":"taqnyat_demo_token_6861d06d31909","response_body":"otp:رمز التحقق غير صحيح"} 
[2025-06-29 23:47:08] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(43): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:47:08] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:47:08] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d07c9c123","username":"a.younis"} 
[2025-06-29 23:47:09] testing.INFO: Taqnyat OTP request response {"status":200,"body":"otp:تم ارسال رمز التحقق لرقم الجوال 7928********","headers":{"Date":["Sun, 29 Jun 2025 23:47:09 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=2bbat7rra7ft3fb9fd5v78lhs4; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Sun, 29 Jun 2025 23:47:09 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["74"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-29 23:47:09] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d07c9c123","user_id":1} 
[2025-06-29 23:47:09] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:47:09] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d07d2ce35","username":"a.younis"} 
[2025-06-29 23:47:09] testing.INFO: Taqnyat OTP request response {"status":200,"body":"otp:لقد تم ارسال رمز التحقق سابقا يمكن المحاوله بعد  60 ثانيه","headers":{"Date":["Sun, 29 Jun 2025 23:47:09 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=2vibperhp3hc45740jkokqpf0t; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Sun, 29 Jun 2025 23:47:09 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["105"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-29 23:47:09] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d07d2ce35","user_id":1} 
[2025-06-29 23:47:09] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861d07d2ce35","user_id":1} 
[2025-06-29 23:47:09] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861d07d2ce35","otp":"123456","username":"a.younis","attempts":1} 
[2025-06-29 23:47:10] testing.INFO: Taqnyat OTP verification response {"status":200,"body":"otp:رمز التحقق غير صحيح","headers":{"Date":["Sun, 29 Jun 2025 23:47:10 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=7s1eho8r9igickfp1qdsja1bcj; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Sun, 29 Jun 2025 23:47:10 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["39"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-29 23:47:10] testing.INFO: Taqnyat OTP verification successful (Demo Mode - Arabic Response) {"session_id":"taqnyat_otp_6861d07d2ce35","otp":"123456","access_token":"taqnyat_demo_token_6861d07e20022","response_body":"otp:رمز التحقق غير صحيح"} 
[2025-06-29 23:47:10] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861d07d2ce35","phone_number":"+************","user_id":1} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:47:10] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:47:10] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d07e3b8ed","username":"a.younis"} 
[2025-06-29 23:47:10] testing.INFO: Taqnyat OTP request response {"status":200,"body":"otp:لقد تم ارسال رمز التحقق سابقا يمكن المحاوله بعد  59 ثانيه","headers":{"Date":["Sun, 29 Jun 2025 23:47:10 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=ggahlph2ijeu4l0db3cghu5qdp; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Sun, 29 Jun 2025 23:47:10 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["105"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-29 23:47:10] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d07e3b8ed","user_id":1} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:59:22] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:59:22] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:59:22] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:59:22] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:59:22] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:59:22] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:59:37] local.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:59:37] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:59:37] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:59:37] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:59:37] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:59:37] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:59:37] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:59:37] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:59:37] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:59:37] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:59:37] local.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:59:37] local.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-30 00:00:08] local.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-30 00:00:08] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:00:08] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:00:08] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:00:08] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:00:08] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:00:08] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:00:08] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:00:08] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:00:08] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:00:08] local.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-30 00:00:08] local.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-30 00:00:32] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-30 00:00:32] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d3a07f6aa","username":"a.younis"} 
[2025-06-30 00:00:33] local.INFO: Taqnyat OTP request response {"status":200,"body":"otp:تم ارسال رمز التحقق لرقم الجوال 7928********","headers":{"Date":["Mon, 30 Jun 2025 00:00:32 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=jk7t8h8uo5t8l4indh6s6m02q0; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:00:33 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["74"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:00:33] local.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d3a07f6aa","user_id":1} 
[2025-06-30 00:01:45] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-30 00:01:45] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d3e9a5e49","username":"a.younis"} 
[2025-06-30 00:01:46] local.INFO: Taqnyat OTP request response {"status":200,"body":"otp:تم ارسال رمز التحقق لرقم الجوال 7928********","headers":{"Date":["Mon, 30 Jun 2025 00:01:46 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=6p46sj99k9s3f089sk46cm4pbs; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:01:46 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["74"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:01:46] local.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d3e9a5e49","user_id":1} 
[2025-06-30 00:02:00] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-30 00:02:00] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d3f8c24b8","username":"a.younis"} 
[2025-06-30 00:02:01] local.INFO: Taqnyat OTP request response {"status":200,"body":"otp:لقد تم ارسال رمز التحقق سابقا يمكن المحاوله بعد  5 ثانيه","headers":{"Date":["Mon, 30 Jun 2025 00:02:01 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=ine67fgdorvi95pprapuqf0afs; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:02:01 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["104"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:02:01] local.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d3f8c24b8","user_id":1} 
