[2025-06-29 01:16:39] testing.ERROR: SQLSTATE[HY000]: General error: 1 no such function: MONTH (Connection: sqlite, SQL: select MONTH(created_at) as month, COUNT(*) as count from "businesses" where strftime('%Y', "created_at") = cast(2025 as text) group by "month" order by "month" asc) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such function: MONTH (Connection: sqlite, SQL: select MONTH(created_at) as month, COUNT(*) as count from \"businesses\" where strftime('%Y', \"created_at\") = cast(2025 as text) group by \"month\" order by \"month\" asc) at /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select MONTH(cr...', Array, Object(Closure))
#1 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select MONTH(cr...', Array, Object(Closure))
#2 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select MONTH(cr...', Array, true)
#3 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3389): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3385): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1052): Illuminate\\Database\\Query\\Builder->pluck('count', 'month')
#7 /Users/<USER>/Herd/admin/business-management-platform/app/Http/Controllers/DashboardController.php(63): Illuminate\\Database\\Eloquent\\Builder->pluck('count', 'month')
#8 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\DashboardController->index()
#9 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), 'index')
#10 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#11 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#12 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#25 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#34 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#36 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(368): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/dashboard', Array, Array, Array, Array)
#60 /Users/<USER>/Herd/admin/business-management-platform/tests/Feature/BusinessTest.php(24): Illuminate\\Foundation\\Testing\\TestCase->get('/dashboard')
#61 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\BusinessTest->test_authenticated_user_can_access_dashboard()
#62 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#63 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#64 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\BusinessTest))
#65 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#66 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#67 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#68 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#69 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#70 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#71 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such function: MONTH at /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Connection.php:404)
[stacktrace]
#0 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): PDO->prepare('select MONTH(cr...')
#1 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select MONTH(cr...', Array)
#2 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select MONTH(cr...', Array, Object(Closure))
#3 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select MONTH(cr...', Array, Object(Closure))
#4 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select MONTH(cr...', Array, true)
#5 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3389): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3385): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1052): Illuminate\\Database\\Query\\Builder->pluck('count', 'month')
#9 /Users/<USER>/Herd/admin/business-management-platform/app/Http/Controllers/DashboardController.php(63): Illuminate\\Database\\Eloquent\\Builder->pluck('count', 'month')
#10 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\DashboardController->index()
#11 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), 'index')
#12 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#13 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#14 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 /Users/<USER>/Herd/admin/business-management-platform/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(368): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/dashboard', Array, Array, Array, Array)
#62 /Users/<USER>/Herd/admin/business-management-platform/tests/Feature/BusinessTest.php(24): Illuminate\\Foundation\\Testing\\TestCase->get('/dashboard')
#63 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\BusinessTest->test_authenticated_user_can_access_dashboard()
#64 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#65 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#66 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\BusinessTest))
#67 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#68 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#69 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#70 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#71 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#72 /Users/<USER>/Herd/admin/business-management-platform/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#73 {main}
"} 
[2025-06-29 22:42:53] local.ERROR: Target class [App\Http\Controllers\TaqnyatAuthController] does not exist. {"userId":1,"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\TaqnyatAuthController] does not exist. at /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php:1019)
[stacktrace]
#0 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(266): Illuminate\\Routing\\Route->getController()
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Herd/admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#55 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\TaqnyatAuthController\" does not exist at /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php:1017)
[stacktrace]
#0 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(1017): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(266): Illuminate\\Routing\\Route->getController()
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 /Users/<USER>/Herd/admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#55 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#56 {main}
"} 
[2025-06-29 22:42:58] local.ERROR: Target class [App\Http\Controllers\TaqnyatAuthController] does not exist. {"userId":1,"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\TaqnyatAuthController] does not exist. at /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php:1019)
[stacktrace]
#0 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(266): Illuminate\\Routing\\Route->getController()
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Herd/admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#55 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\TaqnyatAuthController\" does not exist at /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php:1017)
[stacktrace]
#0 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(1017): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(266): Illuminate\\Routing\\Route->getController()
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 /Users/<USER>/Herd/admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#55 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#56 {main}
"} 
[2025-06-29 22:43:24] local.ERROR: Target class [App\Http\Controllers\Dialog360SettingsController] does not exist. {"userId":1,"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\Dialog360SettingsController] does not exist. at /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php:1019)
[stacktrace]
#0 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(266): Illuminate\\Routing\\Route->getController()
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Herd/admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#55 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Dialog360SettingsController\" does not exist at /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php:1017)
[stacktrace]
#0 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(1017): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Container/Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(266): Illuminate\\Routing\\Route->getController()
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 /Users/<USER>/Herd/admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#55 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#56 {main}
"} 
[2025-06-29 23:07:45] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(38): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:07:45] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:07:47] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c741cd876","user_id":1} 
[2025-06-29 23:07:47] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:07:48] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c7436df00","user_id":1} 
[2025-06-29 23:07:48] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861c7436df00","user_id":1} 
[2025-06-29 23:07:50] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861c7436df00","phone_number":"+************","user_id":1} 
[2025-06-29 23:07:50] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":36} 
[2025-06-29 23:07:50] testing.WARNING: 360Dialog token validation failed {"user_id":1,"token_length":36} 
[2025-06-29 23:07:50] testing.ERROR: Invalid Bearer token. Please check your token and try again. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): Invalid Bearer token. Please check your token and try again. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:84)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/Dialog360SettingsController.php(67): App\\Exceptions\\ApiIntegrationException::invalidToken('360dialog', 'Invalid Bearer ...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\Dialog360SettingsController->updateToken(Object(App\\Http\\Requests\\Dialog360TokenRequest))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dialog360SettingsController), 'updateToken')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/360dialog/...', Array, Array, Array, Array, '{\"bearer_token\"...')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/360dialog/...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(175): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/360dialog/...', Array)
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_360dialog_token_update_with_valid_token()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:07:50] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '{\"phone_number\"...')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(237): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...', Array)
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_phone_number_formatting_in_requests()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:08:59] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(38): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:08:59] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:09:01] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c78bae908","user_id":1} 
[2025-06-29 23:09:01] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:09:02] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c78d15bd2","user_id":1} 
[2025-06-29 23:09:02] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861c78d15bd2","user_id":1} 
[2025-06-29 23:09:03] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861c78d15bd2","phone_number":"+************","user_id":1} 
[2025-06-29 23:09:03] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":36} 
[2025-06-29 23:09:03] testing.WARNING: 360Dialog token validation failed {"user_id":1,"token_length":36} 
[2025-06-29 23:09:03] testing.ERROR: Invalid Bearer token. Please check your token and try again. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): Invalid Bearer token. Please check your token and try again. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:84)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/Dialog360SettingsController.php(67): App\\Exceptions\\ApiIntegrationException::invalidToken('360dialog', 'Invalid Bearer ...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\Dialog360SettingsController->updateToken(Object(App\\Http\\Requests\\Dialog360TokenRequest))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dialog360SettingsController), 'updateToken')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/360dialog/...', Array, Array, Array, Array, '{\"bearer_token\"...')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/360dialog/...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(175): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/360dialog/...', Array)
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_360dialog_token_update_with_valid_token()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:09:03] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:09:05] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c78fd68b4","user_id":1} 
[2025-06-29 23:09:34] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(38): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:09:34] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:09:36] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c7aee6de4","user_id":1} 
[2025-06-29 23:09:36] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:09:37] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c7b0514af","user_id":1} 
[2025-06-29 23:09:37] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861c7b0514af","user_id":1} 
[2025-06-29 23:09:38] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861c7b0514af","phone_number":"+************","user_id":1} 
[2025-06-29 23:09:38] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":36} 
[2025-06-29 23:09:38] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:09:38] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:09:40] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c7b2e3bc0","user_id":1} 
[2025-06-29 23:11:59] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:12:01] local.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c83fd64c0","user_id":1} 
[2025-06-29 23:15:47] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(42): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:15:48] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:15:49] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c92402d50","user_id":1} 
[2025-06-29 23:15:49] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:15:50] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c925684ed","user_id":1} 
[2025-06-29 23:15:50] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861c925684ed","user_id":1} 
[2025-06-29 23:15:51] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861c925684ed","phone_number":"+************","user_id":1} 
[2025-06-29 23:15:51] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":726} 
[2025-06-29 23:15:51] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":726} 
[2025-06-29 23:15:51] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:15:52] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:15:53] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c9280b1dd","user_id":1} 
[2025-06-29 23:15:53] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":726} 
[2025-06-29 23:15:53] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":726} 
[2025-06-29 23:15:53] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:15:53] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":726} 
[2025-06-29 23:15:53] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":726} 
[2025-06-29 23:15:53] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:16:17] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(42): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:16:17] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:16:19] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c941c6d72","user_id":1} 
[2025-06-29 23:16:19] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:16:20] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c9430fccb","user_id":1} 
[2025-06-29 23:16:20] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861c9430fccb","user_id":1} 
[2025-06-29 23:16:21] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861c9430fccb","phone_number":"+************","user_id":1} 
[2025-06-29 23:16:21] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":726} 
[2025-06-29 23:16:21] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":726} 
[2025-06-29 23:16:21] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:16:21] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:16:22] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861c945ba8d8","user_id":1} 
[2025-06-29 23:16:23] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":726} 
[2025-06-29 23:16:23] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":726} 
[2025-06-29 23:16:23] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:16:23] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":726} 
[2025-06-29 23:16:23] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":726} 
[2025-06-29 23:16:23] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:17:27] local.INFO: 360Dialog token update initiated {"user_id":1,"token_length":500} 
[2025-06-29 23:17:28] local.WARNING: 360Dialog token validation failed {"user_id":1,"token_length":500} 
[2025-06-29 23:17:28] local.ERROR: Invalid Bearer token. Please check your token and try again. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): Invalid Bearer token. Please check your token and try again. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:84)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/Dialog360SettingsController.php(67): App\\Exceptions\\ApiIntegrationException::invalidToken('360dialog', 'Invalid Bearer ...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\Dialog360SettingsController->updateToken(Object(App\\Http\\Requests\\Dialog360TokenRequest))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dialog360SettingsController), 'updateToken')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#51 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#52 {main}
"} 
[2025-06-29 23:22:20] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(43): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:22:20] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:22:22] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861caaccfbbd","user_id":1} 
[2025-06-29 23:22:22] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:22:23] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861caae3faad","user_id":1} 
[2025-06-29 23:22:23] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861caae3faad","user_id":1} 
[2025-06-29 23:22:24] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861caae3faad","phone_number":"+************","user_id":1} 
[2025-06-29 23:22:24] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:22:24] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:24] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:24] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:22:24] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:24] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:24] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:22:24] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:24] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:24] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:22:24] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:22:24] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:22:24] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:22:26] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861cab0dc8d5","user_id":1} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:22:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:24:56] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:49] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(43): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:31:49] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:31:50] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861cce51ddf0","user_id":1} 
[2025-06-29 23:31:50] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:31:51] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861cce67387b","user_id":1} 
[2025-06-29 23:31:51] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861cce67387b","user_id":1} 
[2025-06-29 23:31:53] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861cce67387b","phone_number":"+************","user_id":1} 
[2025-06-29 23:31:53] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:31:53] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:53] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:53] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:53] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:53] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:53] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:53] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:53] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:53] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:53] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:31:53] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:31:53] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:31:54] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861cce91fb6c","user_id":1} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:31:54] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:31] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(43): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:32:31] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:32:33] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861cd0fd4540","user_id":1} 
[2025-06-29 23:32:33] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:32:34] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861cd114ff4c","user_id":1} 
[2025-06-29 23:32:34] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861cd114ff4c","user_id":1} 
[2025-06-29 23:32:35] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861cd114ff4c","phone_number":"+************","user_id":1} 
[2025-06-29 23:32:35] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:32:35] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:35] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:35] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:35] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:35] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:35] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:35] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:35] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:35] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:35] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:32:35] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:32:35] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:32:37] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861cd13f2b63","user_id":1} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:32:37] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:34:27] local.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:34:27] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:34:27] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:34:27] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:34:27] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:34:27] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:34:27] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:34:27] local.WARNING: 360Dialog token validation failed {"user_id":1,"token_length":1328} 
[2025-06-29 23:34:27] local.ERROR: Invalid Bearer token. Please check your token and try again. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): Invalid Bearer token. Please check your token and try again. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:84)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/Dialog360SettingsController.php(67): App\\Exceptions\\ApiIntegrationException::invalidToken('360dialog', 'Invalid Bearer ...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\Dialog360SettingsController->updateToken(Object(App\\Http\\Requests\\Dialog360TokenRequest))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dialog360SettingsController), 'updateToken')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#51 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#52 {main}
"} 
[2025-06-29 23:40:04] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:40:04] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:40:04] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:40:04] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:40:04] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:40:04] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:41:37] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:41:37] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:41:37] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:41:37] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:41:37] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:41:37] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:41:37] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:41:37] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:41:37] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:41:37] local.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:45:14] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:45:26] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d016b1dc5","username":"a.younis"} 
[2025-06-29 23:45:27] local.INFO: Taqnyat OTP request response {"status":200,"body":"otp:تم ارسال رمز التحقق لرقم الجوال 7928********","headers":{"Date":["Sun, 29 Jun 2025 23:45:27 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=mkanbvfnfha2tv9qg2fp045sv5; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Sun, 29 Jun 2025 23:45:27 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["74"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-29 23:45:27] local.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861d016b1dc5","otp":"123456","username":"a.younis","attempts":1} 
[2025-06-29 23:45:27] local.INFO: Taqnyat OTP verification response {"status":200,"body":"otp:رمز التحقق غير صحيح","headers":{"Date":["Sun, 29 Jun 2025 23:45:27 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=ovmbv3fv4rj70eu3ts1453vmuv; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Sun, 29 Jun 2025 23:45:27 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["39"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-29 23:46:10] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d04239cc1","username":"a.younis"} 
[2025-06-29 23:46:10] local.INFO: Taqnyat OTP request response {"status":200,"body":"otp:تم ارسال رمز التحقق لرقم الجوال 7928********","headers":{"Date":["Sun, 29 Jun 2025 23:46:10 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=s72icouufjp6m5i0tbm67s05fs; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Sun, 29 Jun 2025 23:46:10 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["74"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-29 23:46:10] local.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861d04239cc1","otp":"123456","username":"a.younis","attempts":1} 
[2025-06-29 23:46:11] local.INFO: Taqnyat OTP verification response {"status":200,"body":"otp:رمز التحقق غير صحيح","headers":{"Date":["Sun, 29 Jun 2025 23:46:11 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=l2dd3hcier33gfiv1ej8n00a72; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Sun, 29 Jun 2025 23:46:11 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["39"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-29 23:46:52] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d06c367c7","username":"a.younis"} 
[2025-06-29 23:46:52] local.INFO: Taqnyat OTP request response {"status":200,"body":"otp:لقد تم ارسال رمز التحقق سابقا يمكن المحاوله بعد  18 ثانيه","headers":{"Date":["Sun, 29 Jun 2025 23:46:52 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=fcs8bfa9hb2e9d9tppfg4kab2n; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Sun, 29 Jun 2025 23:46:52 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["105"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-29 23:46:52] local.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861d06c367c7","otp":"123456","username":"a.younis","attempts":1} 
[2025-06-29 23:46:53] local.INFO: Taqnyat OTP verification response {"status":200,"body":"otp:رمز التحقق غير صحيح","headers":{"Date":["Sun, 29 Jun 2025 23:46:53 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=agsfaej5o73vfdg50b7c3ln344; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Sun, 29 Jun 2025 23:46:53 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["39"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-29 23:46:53] local.INFO: Taqnyat OTP verification successful (Demo Mode - Arabic Response) {"session_id":"taqnyat_otp_6861d06c367c7","otp":"123456","access_token":"taqnyat_demo_token_6861d06d31909","response_body":"otp:رمز التحقق غير صحيح"} 
[2025-06-29 23:47:08] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(43): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-29 23:47:08] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:47:08] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d07c9c123","username":"a.younis"} 
[2025-06-29 23:47:09] testing.INFO: Taqnyat OTP request response {"status":200,"body":"otp:تم ارسال رمز التحقق لرقم الجوال 7928********","headers":{"Date":["Sun, 29 Jun 2025 23:47:09 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=2bbat7rra7ft3fb9fd5v78lhs4; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Sun, 29 Jun 2025 23:47:09 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["74"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-29 23:47:09] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d07c9c123","user_id":1} 
[2025-06-29 23:47:09] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:47:09] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d07d2ce35","username":"a.younis"} 
[2025-06-29 23:47:09] testing.INFO: Taqnyat OTP request response {"status":200,"body":"otp:لقد تم ارسال رمز التحقق سابقا يمكن المحاوله بعد  60 ثانيه","headers":{"Date":["Sun, 29 Jun 2025 23:47:09 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=2vibperhp3hc45740jkokqpf0t; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Sun, 29 Jun 2025 23:47:09 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["105"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-29 23:47:09] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d07d2ce35","user_id":1} 
[2025-06-29 23:47:09] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861d07d2ce35","user_id":1} 
[2025-06-29 23:47:09] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861d07d2ce35","otp":"123456","username":"a.younis","attempts":1} 
[2025-06-29 23:47:10] testing.INFO: Taqnyat OTP verification response {"status":200,"body":"otp:رمز التحقق غير صحيح","headers":{"Date":["Sun, 29 Jun 2025 23:47:10 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=7s1eho8r9igickfp1qdsja1bcj; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Sun, 29 Jun 2025 23:47:10 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["39"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-29 23:47:10] testing.INFO: Taqnyat OTP verification successful (Demo Mode - Arabic Response) {"session_id":"taqnyat_otp_6861d07d2ce35","otp":"123456","access_token":"taqnyat_demo_token_6861d07e20022","response_body":"otp:رمز التحقق غير صحيح"} 
[2025-06-29 23:47:10] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861d07d2ce35","phone_number":"+************","user_id":1} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:47:10] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-29 23:47:10] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d07e3b8ed","username":"a.younis"} 
[2025-06-29 23:47:10] testing.INFO: Taqnyat OTP request response {"status":200,"body":"otp:لقد تم ارسال رمز التحقق سابقا يمكن المحاوله بعد  59 ثانيه","headers":{"Date":["Sun, 29 Jun 2025 23:47:10 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=ggahlph2ijeu4l0db3cghu5qdp; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Sun, 29 Jun 2025 23:47:10 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["105"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-29 23:47:10] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d07e3b8ed","user_id":1} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:47:10] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:59:22] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:59:22] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:59:22] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:59:22] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:59:22] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:59:22] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:59:37] local.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-29 23:59:37] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:59:37] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:59:37] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:59:37] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:59:37] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:59:37] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:59:37] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-29 23:59:37] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-29 23:59:37] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-29 23:59:37] local.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-29 23:59:37] local.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-30 00:00:08] local.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-30 00:00:08] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:00:08] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:00:08] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:00:08] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:00:08] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:00:08] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:00:08] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:00:08] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:00:08] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:00:08] local.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-30 00:00:08] local.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-30 00:00:32] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-30 00:00:32] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d3a07f6aa","username":"a.younis"} 
[2025-06-30 00:00:33] local.INFO: Taqnyat OTP request response {"status":200,"body":"otp:تم ارسال رمز التحقق لرقم الجوال 7928********","headers":{"Date":["Mon, 30 Jun 2025 00:00:32 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=jk7t8h8uo5t8l4indh6s6m02q0; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:00:33 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["74"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:00:33] local.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d3a07f6aa","user_id":1} 
[2025-06-30 00:01:45] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-30 00:01:45] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d3e9a5e49","username":"a.younis"} 
[2025-06-30 00:01:46] local.INFO: Taqnyat OTP request response {"status":200,"body":"otp:تم ارسال رمز التحقق لرقم الجوال 7928********","headers":{"Date":["Mon, 30 Jun 2025 00:01:46 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=6p46sj99k9s3f089sk46cm4pbs; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:01:46 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["74"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:01:46] local.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d3e9a5e49","user_id":1} 
[2025-06-30 00:02:00] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-30 00:02:00] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d3f8c24b8","username":"a.younis"} 
[2025-06-30 00:02:01] local.INFO: Taqnyat OTP request response {"status":200,"body":"otp:لقد تم ارسال رمز التحقق سابقا يمكن المحاوله بعد  5 ثانيه","headers":{"Date":["Mon, 30 Jun 2025 00:02:01 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=ine67fgdorvi95pprapuqf0afs; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:02:01 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["104"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:02:01] local.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d3f8c24b8","user_id":1} 
[2025-06-30 00:08:47] local.INFO: 360Dialog: Fetching user profile  
[2025-06-30 00:08:48] local.INFO: 360Dialog: User profile response {"status":200,"body":"{\"clients\":[{\"id\":\"49BbOTfMCL\",\"name\":\"اكواد العربية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"kfTbavxFCL\",\"name\":\"اكواد العربية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"zjT1lqh2CL\",\"name\":\"شركة صناعات الاغذيه المتحده المحدوده\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"nF5G4xSsCL\",\"name\":\"مؤسسة شبل الورد للتجارة - مساكب\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"ARxfFmatCL\",\"name\":\"شركة سعادة الأليف للتجارة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"Km9sNKg5CL\",\"name\":\"شركة عبدالرحمن سعد الراشد واولاده\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"xS2hN4ebCL\",\"name\":\"adpoultry\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"ZEPtRTlfCL\",\"name\":\"شركة بيوند تكنولوجي لتقنية المعلومات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"CfEcJnC5CL\",\"name\":\"الاهداء الذكي\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"boFd5adUCL\",\"name\":\"مطعم روح الغذاء لتقديم الوجبات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"umSvivymCL\",\"name\":\"اكواد العربية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"mUjiLv73CL\",\"name\":\"جمعية إقراء القرآن الكريم\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"oNzQlQPzCL\",\"name\":\"جمعية تحفيظ بارق\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"lTOOmYmRCL\",\"name\":\"شركة اصل البرجر\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"IRdMJ3QgCL\",\"name\":\"مؤسسة سحابة التحول التقني لتقنية المعلومات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"UpTseANuCL\",\"name\":\"مؤسسة سحابة التحول التقني لتقنية  المعلومات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"y39C1yUQCL\",\"name\":\"شركة ريدان الغذائية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"pQOmVR9TCL\",\"name\":\"علاج الحياة الطبي - Alhayat medical\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"bmeRyn9VCL\",\"name\":\"شركة الموحده للتطوير العقاري\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"hjoUTVtpCL\",\"name\":\"شركة الموحده للتطوير العقاري\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"Ir6UVGLMCL\",\"name\":\"مؤسسة المناخ الملائم للتجارة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"fEEAELfuCL\",\"name\":\"Techsa\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"7b0TmzNBCL\",\"name\":\"مركز الأعمال\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"ua19UjOqCL\",\"name\":\"كناري\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"x1fFhi3aCL\",\"name\":\"شركة التفاصيل السريعة للتجارة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"Euh5TEUNCL\",\"name\":\"مجموعة سوان للعناية بالمرأة للتزيين النسائي\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"VYDIWZQGCL\",\"name\":\"film rent\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"YExVzK7JCL\",\"name\":\"شركة متاجر اوفر للخدمات التجارية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"aYk51uTdCL\",\"name\":\"جمعية زارعي القوقعة - اسمعك\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"5D3iMt3jCL\",\"name\":\"شركة الخنيزان للتجارة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"BABFiAz6CL\",\"name\":\"جمعية خيركم لتعليم القرآن الكريم وتحفيظه\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"QUXtDEOiCL\",\"name\":\"aytamalmuwayh\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"vrB1NKsOCL\",\"name\":\"شركة حراج واحد للخدمات اللوجستية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"i4aDXOIYCL\",\"name\":\"الغرفة التجارية الصناعية بالاحساء\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"qMSGiPOYCL\",\"name\":\"شركة المستجار\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"lzsGFIfHCL\",\"name\":\"canarysa\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"4h87kpEnCL\",\"name\":\"إدارة تنمية الاستثمارات بلدية محافظة العقيق\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"jlNOAwKxCL\",\"name\":\"Taqnyat Jordan\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"8G90079oCL\",\"name\":\"جمعية أصدقاء المرضى بالمدينة المنورة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"1bSQEIk1CL\",\"name\":\"الشركة السعودية لإدارة المرافق شركة شخص واحد\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"iPUC208jCL\",\"name\":\"شركة  البيوت الذكيه\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"cJG2h0hrCL\",\"name\":\"Castrol\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"llPy1vgrCL\",\"name\":\"محامص فضاء البن للتعبئة والتغليف\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"rbApFpykCL\",\"name\":\"فجر التميز\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"lSPaHkuJCL\",\"name\":\"شركة المقابلة الافتراضية لتقنية المعلومات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"SrkOXBgSCL\",\"name\":\"<EMAIL>\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"BRIgrKG8CL\",\"name\":\"شركة الحداد للمزادات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"4mcBFEqtCL\",\"name\":\"مؤسسة علي احمد القبس التجارية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"C3yNTA8XCL\",\"name\":\"جمعية أصدقاء المرضى بالمدينة المنورة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"VQadwr9XCL\",\"name\":\"شركة عطاء التعليمية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"h2aImCrDCL\",\"name\":\"شركة منصة قرار لتقنية المالية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"yg9Wg0oJCL\",\"name\":\"Aljomaih auto rental\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"RVS8L1xnCL\",\"name\":\"Beyond Technology\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"MK6N2oJPCL\",\"name\":\"GExpo Events\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"gm2ouWJXCL\",\"name\":\"شركة الخريف لزيوت التشحيم\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"dgR8RyNvCL\",\"name\":\"شركة الموسى للابواب الاتوماتيكية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"3ecugYYRCL\",\"name\":\"الجمعية الخيرية بمحافظة الكامل\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"pokMJ02wCL\",\"name\":\"kidana\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"70ctGblsCL\",\"name\":\"شركة انظمة جدة للتجارة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"XfJb61gcCL\",\"name\":\"شركة كود الاختراعية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"iceSn2YRCL\",\"name\":\"شركة اصل البرجر لتقديم الوجبات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"L0gwX706CL\",\"name\":\"توب سنتر\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"SNYtMZ15CL\",\"name\":\"مركز ردهة الجمال للتجميل\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"T8dksxdWCL\",\"name\":\"شركة حنانيا للصناعات المعدنية الفنية‎\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"p4DqHqaDCL\",\"name\":\"مدائن فيلج\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"Edhd4DiqCL\",\"name\":\"غرفة حائل التجارية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"h4zDTrlRCL\",\"name\":\"شركة السهم الماسي لخدمات السيارات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"EI7at2umCL\",\"name\":\"zajil expres\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"LeUtWgIZCL\",\"name\":\"شركة اسمنت المنطقة الجنوبية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"VvyHRoqRCL\",\"name\":\"شركة اقطاب الدولية للتجارة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"qpVn1MRMCL\",\"name\":\"شركة عالم البشائر لخدمة حجاج الداخل\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"hZtcblwRCL\",\"name\":\"الغرفة التجارية الصناعية في المنطقة الشرقية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"GKxze9tbCL\",\"name\":\"شركة اقطاب الدولية للتجارة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"A2nWZU4zCL\",\"name\":\"شركة الريادية الابتكارية للتجارة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"7u3VV33RCL\",\"name\":\"شركة عمران للاتصالات و تقنية المعلومات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"RdOxOwQjCL\",\"name\":\"جمعية المكفوفين رؤية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"AFIVJK4NCL\",\"name\":\"مؤسسة جبل زد للتجارة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"DDQbIle9CL\",\"name\":\"جمعية ابطال الخيرية للايتام باملج\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"FzobjXCBCL\",\"name\":\"المجلس الوطني للسلامة والصحة الوطنية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"1rVB68JaCL\",\"name\":\"جمعية تحفيظ القران بالشرقية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"hjncJR04CL\",\"name\":\"شركة وينفيستن المالية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"Fgq5seoACL\",\"name\":\"مجموعه العمري للتجاره و المقاولات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"CQIe5NY5CL\",\"name\":\"APSCO\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"Gvuic1AQCL\",\"name\":\"شركة بداية الشكولاته للتجارة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"r71eZFDiCL\",\"name\":\"أمانة منطقة حائل\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"lBqZQ17RCL\",\"name\":\"الشركة الوطنية للتربية و التعليم\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"XhdaXc71CL\",\"name\":\"جمعية ترميم للتنمية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"iHxee8ucCL\",\"name\":\"شركة مدائن العصر العقارية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"bvxGIXaOCL\",\"name\":\"مدد للاعمال\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"jk8wgl1RCL\",\"name\":\"شركة الصناعة الذكية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"1456HKFnCL\",\"name\":\"جمعية ترميم للتنمية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"UQxnupkPCL\",\"name\":\"شركة البسمة الخالدة لتجارة الجملة والتجزئة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"TfxvO1UJCL\",\"name\":\"Tatweer Transport Services Company\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"QxO3uhUCCL\",\"name\":\"العربية للتنمية والاستثمار\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"GTNy8DLuCL\",\"name\":\"جمعية مضر الخيرية للخدمات الاجتماعية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"vizd4KSaCL\",\"name\":\"Nawat Aloud - نواة العود\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"ig2oyTO5CL\",\"name\":\"غرفة تبوك\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"yoa2P5HeCL\",\"name\":\"شركة عبدالمجيد عبدالرحمن الجريسي للاستقدام\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"xGc7XsA9CL\",\"name\":\"ghyath\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"JivESzLqCL\",\"name\":\"غرفة تبوك\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"oUdt0QcUCL\",\"name\":\"شركه المتطوره للتشغيل والصيانه\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"H5yPaDY7CL\",\"name\":\"امانة المدينه المنورة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"ylTkonKfCL\",\"name\":\"عبور الخليج\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"pd9W2EV2CL\",\"name\":\"Eastren Amana_أمانة المنطقة الشرقية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"O70m31LeCL\",\"name\":\"AytamSanad\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"RswOZgDbCL\",\"name\":\"شركة قصر المساكن للمقاولات العامة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"UxOs8CkfCL\",\"name\":\"Yakaza\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"B29vTy2KCL\",\"name\":\"Basateen Hail\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"EPH7rmGMCL\",\"name\":\"innvopc\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"pwWHmerPCL\",\"name\":\"Saudi Ceramics Company\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"NcIY1VOPCL\",\"name\":\"شركة عجلان الدولية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"trBmNXvSCL\",\"name\":\"مجمع شار الطبي\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"9sXemJkQCL\",\"name\":\"JEVAN جفان\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"TQiJHPKPCL\",\"name\":\"الجمعية الخيرية لتحفيظ القران الكريم بالمنطقة الشرقية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"p3WDggVrCL\",\"name\":\"ِشركة طبيب عن بعد ذ م م\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"GreUeU7uCL\",\"name\":\"شركة الجميح للسيارات المحدودة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"zQ5WKbTKCL\",\"name\":\"مؤسسة ميم عين للتعليم\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"EQA0kNFbCL\",\"name\":\"مدائن فيلج\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"wYPCa90vCL\",\"name\":\"جمعية أصدقاء المرضى بالمدينة المنورة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"ELvh9FdLCL\",\"name\":\"جمعية أصدقاء المرضى بالمدينة المنورة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"EMYFXL2ACL\",\"name\":\"شركة مجموعة الحصان للتعليم والتدريب المحدودة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"VN7TzaM7CL\",\"name\":\"شركة الغنيم المحدودة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"BFMjcNyrCL\",\"name\":\"شركة الريادية الابتكارية للتجارة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"uQaTHdhaCL\",\"name\":\"شركة منصة قرار لتقنية المالية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"yCP1LaIuCL\",\"name\":\"مؤسسة متابعة طلابي لتقنية نظم المعلومات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"CHFy1STBCL\",\"name\":\"الجمعية الخيريه لتحفيظ القران الكريم بالدمام\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"R4Sy4e2gCL\",\"name\":\"ورق عنب ليليا\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"GzsbazIuCL\",\"name\":\"ملتقى مستقبل القطاع غير الربحي\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"vIDQ1cS8CL\",\"name\":\"صالون لينات للتزيين النسائي\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"kaqQYsibCL\",\"name\":\"بلدية محافظة العقيق\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"AQYcuGYPCL\",\"name\":\"شركة خدمات الآلية للتشغيل والصيانة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"UasFVdknCL\",\"name\":\"الشركة السعودية للخدمات الارضية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"NbYDOkwXCL\",\"name\":\"كبتار\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"oq6vtoUACL\",\"name\":\"جمعية الجبيل\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"4svFaHBrCL\",\"name\":\"جمعية رفادة لخدمة الحاج والمعتمر\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"Jyip7XRhCL\",\"name\":\"نواة العود\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"vlrd8jpBCL\",\"name\":\"AL-MAKTAB AL-FAKHM For Furniture\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"kDlpfgAwCL\",\"name\":\"مؤسسة أثر المياه\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"xvKxDMulCL\",\"name\":\"الجمعية الخيرية لتحفيظ القرآن الكريم بشرورة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"jszsZ2R3CL\",\"name\":\"مركز البلاغات والطوارئ 940 - أمانة المنطقة الشرقة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"ZSq3OwMcCL\",\"name\":\"جمعية القطيف الخيرية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"Nd31eJbkCL\",\"name\":\"جمعية تحفيظ القرآن الكريم بالأحساء\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"eFxOLvY3CL\",\"name\":\"غرفة نجران\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"sNeZ2ilrCL\",\"name\":\"شركة الاستقدام الدولية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"lDixG6qnCL\",\"name\":\"المطاحن الأولى\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"EoD9kDXkCL\",\"name\":\"شركة محيط البيانات لتقنية المعلومات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"uYcY94EVCL\",\"name\":\"شركة الموحدة الضيافة التجارية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"HaKuxBLJCL\",\"name\":\"عيادات بلغصون\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"A1IYx2UMCL\",\"name\":\"شركة المقابلة الافتراضية لتقنية المعلومات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"FjYoO9IuCL\",\"name\":\"Saudi Bulk\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"78JhEE0rCL\",\"name\":\"Majestic Furniture\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"yJqGQ5VQCL\",\"name\":\"شركة الفقاري القابضة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"JZ9Fw50xCL\",\"name\":\"شركة سنام للاعمال\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"0Qkm3sXjCL\",\"name\":\"مركز الفاحص المتميز لصيانة السيارات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"dPOcCTbeCL\",\"name\":\"شركة الاستقدام الدولية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"PDiHlstOCL\",\"name\":\"شركة سر الابتسامة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"jVHvo2kWCL\",\"name\":\"شركة وينفيستن المالية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"pHVM0LLgCL\",\"name\":\"Taqnyat demo\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"qcUH47imCL\",\"name\":\"شركة اقطاب الدولية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"Pll7THOECL\",\"name\":\"خياط الديرمان للخياطة الرجالية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"552nPv5iCL\",\"name\":\"جمعية اعتدال لحفظ النعمة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"dDICzgGYCL\",\"name\":\"البر بحفر الباطن\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"k6CP5amNCL\",\"name\":\"مجمع عيادات مستشارك المتقدم لطب الاسنان\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"UrFSOHxrCL\",\"name\":\"ترميم Tarmeem\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"bo4PRH37CL\",\"name\":\"منتدى عسير للاستثمار\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"PLWAuNvrCL\",\"name\":\"Fuchi Stores\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"gJ4Ow2ruCL\",\"name\":\"شركة إمكان العربية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"A13TvkMUCL\",\"name\":\"غرفة الأحساء\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"oFnn1UYzCL\",\"name\":\"Rising Stars\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"rmVind3TCL\",\"name\":\"شركة مرباب العقارية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"VjAcbvLYCL\",\"name\":\"The Factory Furniture\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"o6WTfRyQCL\",\"name\":\"مؤسسة محمد عبدالرزاق بشاوري للنظارات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"MIDIas1PCL\",\"name\":\"Jeddah University\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"GHQJa210CL\",\"name\":\"كبتار - Kebtar\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"T1A9wsEQCL\",\"name\":\"شركة كاتر يون للتموين القابضة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"3lwwR5LdCL\",\"name\":\"Adeed_HC\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"dia7eYHxCL\",\"name\":\"وحيد لخدمات الأعمال\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"xiOyFVogCL\",\"name\":\"taqnyat test\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"CG7FtQ99CL\",\"name\":\"almustaqbal\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"akqr1p0HCL\",\"name\":\"j-athar\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"wNKfmyybCL\",\"name\":\"جمعية تحفيظ القران الشرقية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"CwjeVw8sCL\",\"name\":\"مكتب الاتصال المباشر لتحصيل الديون\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"XOe41ikwCL\",\"name\":\"ايساد \",\"partner_id\":\"GbM78BPA\"},{\"id\":\"fn9vssHKCL\",\"name\":\"Al Tababah Center\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"FPtgDwBxCL\",\"name\":\"المركز الامريكي للتجميل والابتسامة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"WYcfwyM1CL\",\"name\":\"Anas\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"NkG53lTjCL\",\"name\":\"شركة الوجبة القيمة لتقنية المعلومات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"dVBD7PLDCL\",\"name\":\"تكملات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"x19ocjgKCL\",\"name\":\"بيت قرمشة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"HGWFMkZqCL\",\"name\":\"Depo\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"nf191d2UCL\",\"name\":\"Tatweer Transit\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"dXQNhtdWCL\",\"name\":\"3rd Millennium Engineering Consultants\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"dCFkanHwCL\",\"name\":\"عطور عين\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"BmqnDtNYCL\",\"name\":\"المدرسة الوطنية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"Sct539SCCL\",\"name\":\"Honda\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"5acmX7BBCL\",\"name\":\"كرك محبوب\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"C03XVklxCL\",\"name\":\"شركة صلني\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"vIy0X6mhCL\",\"name\":\"الاهداء الذكي\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"07BBPwdBCL\",\"name\":\"شركة بدر البلوي لقطع غيار الشاحنات و المقطورات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"3jKS2DazCL\",\"name\":\"شركة ندى الشروق للتجارة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"oukjFRKgCL\",\"name\":\"جامعة الملك فهد للبترول والمعادن\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"tjkG3h2FCL\",\"name\":\"شركة تداول الامتياز لتقنية  المعلومات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"ABRuBRn5CL\",\"name\":\"شركة مركز ادمه لزراعة الشعر المحدودة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"tRw7iti9CL\",\"name\":\"قسم التحقيقات بإدارة عمليات الموارد البشرية بالشركة السعودية للخدمات الأرضية – Saudi Ground Services Company (HR Investigation Section)\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"LmbeoxIFCL\",\"name\":\"جمعية أصدقاء المرضى بالمدينة المنورة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"N9xTDfqMCL\",\"name\":\"Pet Products\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"7jnEzvxGCL\",\"name\":\"الجمعية الخيرية للرعاية الصحية الأولية (درهم وقاية)\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"LJPeFddDCL\",\"name\":\"مطعم نخبة الأطعمة لتقديم الوجبات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"AJ56cAy9CL\",\"name\":\"قسم التحقيقات بإدارة عمليات الموارد البشرية بالشركة السعودية للخدمات الأرضية – Saudi Ground\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"n8qtgxbmCL\",\"name\":\"شركة نفوذ للمنتجات الغذائية مساهمة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"lbkFAHMkCL\",\"name\":\"شركة كلاسيكية الخاصة المحدودة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"To02uN2YCL\",\"name\":\"شركة فجر التميز لتنظيم المعارض والمؤتمرات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"dBbOyx06CL\",\"name\":\"جمعية درهم وقاية الصحية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"9l0HfLe0CL\",\"name\":\"بلدية محافظة العقيق\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"rG56DbWbCL\",\"name\":\"شركة غذاء السلطان\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"i9gDU2OaCL\",\"name\":\"الجمعية الخيرية بمحافظة الكامل\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"Dl3UWJsDCL\",\"name\":\"شركة منصة ديما الدولية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"y77kBTSMCL\",\"name\":\"شركة النوادي التفاعلية لتقنية المعلومات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"YHZrTquQCL\",\"name\":\"شركة منصة قرار لتقنية المالية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"6OpTdyTfCL\",\"name\":\"مدائن فيلج\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"0r7kSjoICL\",\"name\":\"مطاعم الشيف إياد\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"4RCb7eNMCL\",\"name\":\"مؤسسة حشوة فطيره للوجبات السريعة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"lUHfHwnECL\",\"name\":\"test\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"BH4iyCGICL\",\"name\":\"شركة نفوذ للمنتجات الغذائية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"D2nAno0uCL\",\"name\":\"Sultan Delight Burger\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"k2CbJvOvCL\",\"name\":\"Sultan Delight Burger\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"AW1SuNnhCL\",\"name\":\"test2\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"bXDpeQa6CL\",\"name\":\"فرسان السليمانية للإطارات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"aX60y7kTCL\",\"name\":\"Chef Eyad Restaurant\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"04hNPQAUCL\",\"name\":\"شركة بارك العالمية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"bx0SCEIzCL\",\"name\":\"شركة مسكان الصناعية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"QmRTKwHACL\",\"name\":\"جامعة رياض العلم\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"VaHMYGp3CL\",\"name\":\"شركة عطر المدى للزهور\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"7pGJdMnmCL\",\"name\":\"مكتب الالفية الثالثة للاستشارات الهندسية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"yBXgLYOjCL\",\"name\":\"شركة المقابلة الافتراضية لتقنية المعلومات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"xbZnfkZMCL\",\"name\":\"البيت الأنيق\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"ZNEvmmg1CL\",\"name\":\"شركة الخدمات المبتكرة للتشغيل والصيانة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"NLEPYuu2CL\",\"name\":\"الشركة العالمية لمواد البناء المحدودة ( بينكس )\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"TafzTT13CL\",\"name\":\"مؤسسة جيل زد للتجارة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"3eNMBBwMCL\",\"name\":\"taqnyat Mobile information company LTD\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"RMVyDQZ8CL\",\"name\":\"شركة نجوم العلا\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"BSgVkeT9CL\",\"name\":\"جمعية أصدقاء المرضى بالمدينة المنورة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"vqFSz8OICL\",\"name\":\"شركة المطاحن الحديثة للمنتجات الغذائية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"gOF4aQPjCL\",\"name\":\"مستشفى د نور محمد خان العام\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"EU3qmpnvCL\",\"name\":\"جامعة عفت\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"HroLIqV6CL\",\"name\":\"مكيال المالية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"e8OnOBe4CL\",\"name\":\"مؤسسة إبراهيم عبدالله إبراهيم العنزان لتأجير المعدات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"wnnROUOUCL\",\"name\":\"Wareef Organization\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"F57OhVP3CL\",\"name\":\"صالون مشاعر جميلة للتزيين النسائي\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"GdMkxSczCL\",\"name\":\"شركة تطبيق الجمال لتقنية المعلومات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"fZ04wStsCL\",\"name\":\"مؤسسة سجاد الفخامة للسجاد\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"DFo5S17RCL\",\"name\":\"شركة المأمون لوساطة التأمين المحدودة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"X1B4gvcNCL\",\"name\":\"الشركة العربية للتنمية والاستثمار\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"XTFLxHEJCL\",\"name\":\"شركة تطبيق الجمال لتقنية المعلومات\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"DsNPzRrcCL\",\"name\":\"شركة كل الحماية للصناعة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"1YicVS0xCL\",\"name\":\"نظرية الابداع\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"vGvfbPhECL\",\"name\":\"شركة بيوت السكينة التجارية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"2WiEfCXECL\",\"name\":\"Company AL-MUBAIT For Travel and tourism\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"ESropwQrCL\",\"name\":\"Pet Products\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"Se5TucwQCL\",\"name\":\"Growth Plus\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"jt3pywAuCL\",\"name\":\"شركة منصة تريلا للخدمات اللوجستية\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"ywZgCXTKCL\",\"name\":\"agents on cloud\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"Hyl8Fsk4CL\",\"name\":\"كرك محبوب\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"SpJZ1E26CL\",\"name\":\"3rd Millennium Engineering Consultants\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"RDybwzghCL\",\"name\":\"filters for car services\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"dzro8hDmCL\",\"name\":\"Taqnyat-Demo\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"3P9U6DypCL\",\"name\":\"Rootura\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"5ehrPFQVCL\",\"name\":\"FlyDay\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"EbxLsInqCL\",\"name\":\"سنام الأعمال القابضة\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"pvat6ymHCL\",\"name\":\"Suber\",\"partner_id\":\"GbM78BPA\"},{\"id\":\"MVYVT40uCL\",\"name\":\"امانة جدة\",\"partner_id\":\"GbM78BPA\"}],\"active_partner\":\"GbM78BPA\",\"organizations\":[{\"partner_id\":\"GbM78BPA\",\"org_name\":\"gbm78bpa\"}],\"permissions\":[\"api_keys:manage\",\"audit_log:create\",\"bundles:read\",\"bundles:write\",\"notifications:subscribe\",\"partner:manage\",\"tos:accept\",\"tos:read\",\"user:manage\"],\"roles\":[\"partner-member\"],\"email\":\"<EMAIL>\",\"email_verified\":true,\"picture\":\"https://s.gravatar.com/avatar/72d89a33f52c3ce67835029e8f646af8?s=480&r=pg&d=https%3A%2F%2Fcdn.auth0.com%2Favatars%2Fan.png\",\"name\":\"Anas\",\"nickname\":\"a.younis.taqnyat.sa\",\"last_managed\":{\"channel_id\":null}}"} 
[2025-06-30 00:08:48] local.INFO: 360Dialog: Fetching account details {"partner_id":"GbM78BPA","offset":0,"limit":50} 
[2025-06-30 00:08:50] local.INFO: 360Dialog: Account details response {"status":200,"body_length":256831} 
[2025-06-30 00:08:50] local.INFO: 360Dialog: Fetching traffic data {"partner_id":"GbM78BPA","client_id":"3eNMBBwMCL","project_id":"9tJH3tPR","channel_id":"fCFWoZCH"} 
[2025-06-30 00:08:51] local.INFO: 360Dialog: Traffic data response {"status":200,"body_length":25688} 
[2025-06-30 00:11:29] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d631d2434","username":"a.younis"} 
[2025-06-30 00:11:30] local.INFO: Taqnyat OTP request response {"status":200,"body":"otp:تم ارسال رمز التحقق لرقم الجوال 7928********","headers":{"Date":["Mon, 30 Jun 2025 00:11:30 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=tgs1guvun4emfdc3j818m3rtpf; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:11:30 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["74"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:11:30] local.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861d631d2434","otp":"123456","username":"a.younis","attempts":1} 
[2025-06-30 00:11:30] local.INFO: Taqnyat OTP verification response {"status":200,"body":"otp:رمز التحقق غير صحيح","headers":{"Date":["Mon, 30 Jun 2025 00:11:30 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=8ikjp1p09cfgkufjge55jpl74p; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:11:30 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["39"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:11:30] local.INFO: Taqnyat OTP verification - analyzing response {"response_body":"otp:رمز التحقق غير صحيح","response_length":39,"otp_submitted":"123456"} 
[2025-06-30 00:11:30] local.INFO: Taqnyat OTP verification - Arabic error detected {"arabic_error":"رمز التحقق غير صحيح","english_message":"Invalid OTP code. Please check and try again.","response_body":"otp:رمز التحقق غير صحيح"} 
[2025-06-30 00:11:30] local.INFO: Taqnyat OTP verification successful (Demo Mode - Arabic Response) {"session_id":"taqnyat_otp_6861d631d2434","otp":"123456","access_token":"taqnyat_demo_token_6861d632dcf2d","response_body":"otp:رمز التحقق غير صحيح"} 
[2025-06-30 00:11:56] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d64ceef81","username":"a.younis"} 
[2025-06-30 00:11:57] local.INFO: Taqnyat OTP request response {"status":200,"body":"otp:لقد تم ارسال رمز التحقق سابقا يمكن المحاوله بعد  33 ثانيه","headers":{"Date":["Mon, 30 Jun 2025 00:11:57 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=v72dgi5htst9f6gqgff83gi5fr; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:11:57 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["105"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:11:57] local.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861d64ceef81","otp":"999999","username":"a.younis","attempts":1} 
[2025-06-30 00:11:58] local.INFO: Taqnyat OTP verification response {"status":200,"body":"otp:رمز التحقق غير صحيح","headers":{"Date":["Mon, 30 Jun 2025 00:11:57 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=ndfii8uqsvlm5dv1jltvp3vhta; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:11:58 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["39"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:11:58] local.INFO: Taqnyat OTP verification - analyzing response {"response_body":"otp:رمز التحقق غير صحيح","response_length":39,"otp_submitted":"999999"} 
[2025-06-30 00:11:58] local.INFO: Taqnyat OTP verification - Arabic error detected {"arabic_error":"رمز التحقق غير صحيح","english_message":"Invalid OTP code. Please check and try again.","response_body":"otp:رمز التحقق غير صحيح"} 
[2025-06-30 00:11:58] local.WARNING: Taqnyat OTP verification failed {"session_id":"taqnyat_otp_6861d64ceef81","otp":"999999","response_body":"otp:رمز التحقق غير صحيح","error_message":"Invalid OTP code. Please check and try again.","attempts":1,"max_attempts":3} 
[2025-06-30 00:12:12] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(43): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-30 00:12:12] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-30 00:12:12] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d65cb9bb5","username":"a.younis"} 
[2025-06-30 00:12:13] testing.INFO: Taqnyat OTP request response {"status":200,"body":"otp:تم ارسال رمز التحقق لرقم الجوال 7928********","headers":{"Date":["Mon, 30 Jun 2025 00:12:13 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=9lbptdqgilp6gss5hrd0fmcul4; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:12:13 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["74"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:12:13] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d65cb9bb5","user_id":1} 
[2025-06-30 00:12:13] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-30 00:12:13] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d65d871d9","username":"a.younis"} 
[2025-06-30 00:12:14] testing.INFO: Taqnyat OTP request response {"status":200,"body":"otp:لقد تم ارسال رمز التحقق سابقا يمكن المحاوله بعد  59 ثانيه","headers":{"Date":["Mon, 30 Jun 2025 00:12:14 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=huutk9g3ap4j4ke9fjue9k9pnh; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:12:14 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["105"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:12:14] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d65d871d9","user_id":1} 
[2025-06-30 00:12:14] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861d65d871d9","user_id":1} 
[2025-06-30 00:12:14] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861d65d871d9","otp":"123456","username":"a.younis","attempts":1} 
[2025-06-30 00:12:14] testing.INFO: Taqnyat OTP verification response {"status":200,"body":"otp:رمز التحقق غير صحيح","headers":{"Date":["Mon, 30 Jun 2025 00:12:14 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=omi8uvrh097t54434hnoe4vte6; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:12:14 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["39"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:12:14] testing.INFO: Taqnyat OTP verification - analyzing response {"response_body":"otp:رمز التحقق غير صحيح","response_length":39,"otp_submitted":"123456"} 
[2025-06-30 00:12:14] testing.INFO: Taqnyat OTP verification - Arabic error detected {"arabic_error":"رمز التحقق غير صحيح","english_message":"Invalid OTP code. Please check and try again.","response_body":"otp:رمز التحقق غير صحيح"} 
[2025-06-30 00:12:14] testing.INFO: Taqnyat OTP verification successful (Demo Mode - Arabic Response) {"session_id":"taqnyat_otp_6861d65d871d9","otp":"123456","access_token":"taqnyat_demo_token_6861d65e7f320","response_body":"otp:رمز التحقق غير صحيح"} 
[2025-06-30 00:12:14] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861d65d871d9","phone_number":"+************","user_id":1} 
[2025-06-30 00:12:14] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-30 00:12:14] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:14] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:14] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:14] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:14] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:14] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:14] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:14] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:14] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:14] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-30 00:12:14] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-30 00:12:14] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-30 00:12:14] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d65ea1bbb","username":"a.younis"} 
[2025-06-30 00:12:15] testing.INFO: Taqnyat OTP request response {"status":200,"body":"otp:لقد تم ارسال رمز التحقق سابقا يمكن المحاوله بعد  58 ثانيه","headers":{"Date":["Mon, 30 Jun 2025 00:12:15 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=rcck07eku3hdhtdto6420l33uc; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:12:15 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["105"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:12:15] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d65ea1bbb","user_id":1} 
[2025-06-30 00:12:15] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-30 00:12:15] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-30 00:12:15] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-30 00:12:15] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:15] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:50] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(43): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-30 00:12:50] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-30 00:12:50] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d682794c3","username":"a.younis"} 
[2025-06-30 00:12:51] testing.INFO: Taqnyat OTP request response {"status":200,"body":"otp:لقد تم ارسال رمز التحقق سابقا يمكن المحاوله بعد  22 ثانيه","headers":{"Date":["Mon, 30 Jun 2025 00:12:51 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=tfeeu8h6n4jps3q70sdnrol6hd; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:12:51 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["105"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:12:51] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d682794c3","user_id":1} 
[2025-06-30 00:12:51] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-30 00:12:51] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d68313576","username":"a.younis"} 
[2025-06-30 00:12:51] testing.INFO: Taqnyat OTP request response {"status":200,"body":"otp:لقد تم ارسال رمز التحقق سابقا يمكن المحاوله بعد  22 ثانيه","headers":{"Date":["Mon, 30 Jun 2025 00:12:51 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=b862jiti46gn683pl0pe2b7ao2; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:12:51 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["105"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:12:51] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d68313576","user_id":1} 
[2025-06-30 00:12:51] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861d68313576","user_id":1} 
[2025-06-30 00:12:51] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861d68313576","otp":"123456","username":"a.younis","attempts":1} 
[2025-06-30 00:12:52] testing.INFO: Taqnyat OTP verification response {"status":200,"body":"otp:رمز التحقق غير صحيح","headers":{"Date":["Mon, 30 Jun 2025 00:12:52 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=svpipa6ig4s2djsla0tqi8mkjb; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:12:52 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["39"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:12:52] testing.INFO: Taqnyat OTP verification - analyzing response {"response_body":"otp:رمز التحقق غير صحيح","response_length":39,"otp_submitted":"123456"} 
[2025-06-30 00:12:52] testing.INFO: Taqnyat OTP verification - Arabic error detected {"arabic_error":"رمز التحقق غير صحيح","english_message":"Invalid OTP code. Please check and try again.","response_body":"otp:رمز التحقق غير صحيح"} 
[2025-06-30 00:12:52] testing.INFO: Taqnyat OTP verification successful (Demo Mode - Arabic Response) {"session_id":"taqnyat_otp_6861d68313576","otp":"123456","access_token":"taqnyat_demo_token_6861d6840a71d","response_body":"otp:رمز التحقق غير صحيح"} 
[2025-06-30 00:12:52] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861d68313576","phone_number":"+************","user_id":1} 
[2025-06-30 00:12:52] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-30 00:12:52] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-30 00:12:52] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-30 00:12:52] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d68424b0d","username":"a.younis"} 
[2025-06-30 00:12:52] testing.INFO: Taqnyat OTP request response {"status":200,"body":"otp:لقد تم ارسال رمز التحقق سابقا يمكن المحاوله بعد  21 ثانيه","headers":{"Date":["Mon, 30 Jun 2025 00:12:52 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=rb4d3k6kiejfdom30rtv0r5e50; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:12:52 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["105"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:12:52] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d68424b0d","user_id":1} 
[2025-06-30 00:12:52] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-30 00:12:52] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-30 00:12:52] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-30 00:12:52] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:12:52] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:18:23] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:18:23] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:18:23] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:18:23] local.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:18:23] local.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:18:23] local.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:19:11] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-30 00:19:11] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d7ff267c3","username":"a.younis"} 
[2025-06-30 00:19:11] local.INFO: Taqnyat OTP request response {"status":200,"body":"otp:تم ارسال رمز التحقق لرقم الجوال 7928********","headers":{"Date":["Mon, 30 Jun 2025 00:19:11 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=v6p70g8jgfucpfp5nqq2ktb7e4; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:19:11 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["74"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:19:11] local.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d7ff267c3","user_id":1} 
[2025-06-30 00:23:15] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d8f38722c","username":"a.younis"} 
[2025-06-30 00:23:16] local.INFO: Taqnyat OTP request response {"status":200,"body":"otp:تم ارسال رمز التحقق لرقم الجوال 7928********","headers":{"Date":["Mon, 30 Jun 2025 00:23:16 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=cjgrqpmoj8cmqm47p5vtjk3ucc; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:23:16 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["74"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:23:16] local.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861d8f38722c","otp":"123456","username":"a.younis","attempts":1} 
[2025-06-30 00:23:16] local.INFO: Taqnyat OTP verification - sending request with cookies {"cookies":"_clck=py1yak%7C2%7Cfwn%7C0%7C1953; ticketTypePay=8%2C13%2C21%2C24%2C27%2C32%2C35%2C39%2C46%2C47%2C51%2C66%2C70%2C80%2C82%2C84%2C86%2C88%2C90%2C92%2C94%2C96%2C98%2C100%2C102%2C104%2C106%2C107%2C109%2C111%2C113%2C114%2C116%2C118%2C120%2C122%2C124%2C126%2C130%2C132%2C134%2C136%2C138%2C140%2C142%2C144%2C146%2C148%2C152%2C154%2C156%2C158%2C160%2C162%2C164%2C166%2C168%2C170%2C172%2C174%2C176%2C178%2C181%2C183%2C185%2C187%2C189%2C191%2C193%2C195%2C197%2C199%2C201%2C203%2C205%2C207%2C209%2C211%2C213%2C214%2C216%2C218%2C220%2C222%2C224%2C226%2C244%2C246%2C248%2C250%2C252%2C254%2C256%2C258%2C260%2C262%2C264%2C266%2C268%2C270%2C; power=-; userNameT=a.younis; refreshPage=0; PHPSESSID=taqnyat_session_6861d8f41ff35","username":"a.younis","otp":"123456"} 
[2025-06-30 00:23:16] local.INFO: Taqnyat OTP verification response {"status":200,"body":"otp:رمز التحقق غير صحيح","headers":{"Date":["Mon, 30 Jun 2025 00:23:16 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Last-Modified":["Mon, 30 Jun 2025 00:23:16 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["39"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:23:16] local.INFO: Taqnyat OTP verification - analyzing response {"response_body":"otp:رمز التحقق غير صحيح","response_length":39,"otp_submitted":"123456"} 
[2025-06-30 00:23:16] local.INFO: Taqnyat OTP verification - Demo mode activated {"otp":"123456","response_body":"otp:رمز التحقق غير صحيح"} 
[2025-06-30 00:23:16] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d8f495a99","username":"a.younis"} 
[2025-06-30 00:23:17] local.INFO: Taqnyat OTP request response {"status":200,"body":"otp:لقد تم ارسال رمز التحقق سابقا يمكن المحاوله بعد  59 ثانيه","headers":{"Date":["Mon, 30 Jun 2025 00:23:17 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=34bjriu4ldp23bcgtleisfmvin; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:23:17 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["105"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:23:17] local.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861d8f495a99","otp":"999999","username":"a.younis","attempts":1} 
[2025-06-30 00:23:17] local.INFO: Taqnyat OTP verification - sending request with cookies {"cookies":"_clck=py1yak%7C2%7Cfwn%7C0%7C1953; ticketTypePay=8%2C13%2C21%2C24%2C27%2C32%2C35%2C39%2C46%2C47%2C51%2C66%2C70%2C80%2C82%2C84%2C86%2C88%2C90%2C92%2C94%2C96%2C98%2C100%2C102%2C104%2C106%2C107%2C109%2C111%2C113%2C114%2C116%2C118%2C120%2C122%2C124%2C126%2C130%2C132%2C134%2C136%2C138%2C140%2C142%2C144%2C146%2C148%2C152%2C154%2C156%2C158%2C160%2C162%2C164%2C166%2C168%2C170%2C172%2C174%2C176%2C178%2C181%2C183%2C185%2C187%2C189%2C191%2C193%2C195%2C197%2C199%2C201%2C203%2C205%2C207%2C209%2C211%2C213%2C214%2C216%2C218%2C220%2C222%2C224%2C226%2C244%2C246%2C248%2C250%2C252%2C254%2C256%2C258%2C260%2C262%2C264%2C266%2C268%2C270%2C; power=-; userNameT=a.younis; refreshPage=0; PHPSESSID=taqnyat_session_6861d8f514c10","username":"a.younis","otp":"999999"} 
[2025-06-30 00:23:17] local.INFO: Taqnyat OTP verification response {"status":200,"body":"otp:رمز التحقق غير صحيح","headers":{"Date":["Mon, 30 Jun 2025 00:23:17 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Last-Modified":["Mon, 30 Jun 2025 00:23:17 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["39"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:23:17] local.INFO: Taqnyat OTP verification - analyzing response {"response_body":"otp:رمز التحقق غير صحيح","response_length":39,"otp_submitted":"999999"} 
[2025-06-30 00:23:17] local.INFO: Taqnyat OTP verification - Arabic error detected {"arabic_error":"رمز التحقق غير صحيح","english_message":"Invalid OTP code. Please check and try again.","response_body":"otp:رمز التحقق غير صحيح"} 
[2025-06-30 00:23:17] local.WARNING: Taqnyat OTP verification failed {"session_id":"taqnyat_otp_6861d8f495a99","otp":"999999","response_body":"otp:رمز التحقق غير صحيح","error_message":"Invalid OTP code. Please check and try again.","attempts":1,"max_attempts":3} 
[2025-06-30 00:23:24] testing.ERROR: No phone number associated with your account. Please update your profile with a valid phone number. {"userId":1,"exception":"[object] (App\\Exceptions\\ApiIntegrationException(code: 0): No phone number associated with your account. Please update your profile with a valid phone number. at /Users/<USER>/Herd/admin/app/Exceptions/ApiIntegrationException.php:136)
[stacktrace]
#0 /Users/<USER>/Herd/admin/app/Http/Controllers/TaqnyatAuthController.php(33): App\\Exceptions\\ApiIntegrationException::validationFailed('taqnyat', 'No phone number...')
#1 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\TaqnyatAuthController->requestOtp(Object(Illuminate\\Http\\Request))
#2 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TaqnyatAuthController), 'requestOtp')
#3 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#4 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#5 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/taqnyat/au...', Array, Array, Array, Array, '[]')
#51 /Users/<USER>/Herd/admin/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/taqnyat/au...', Array, Array, 0)
#52 /Users/<USER>/Herd/admin/tests/Feature/ApiIntegrationTest.php(43): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/taqnyat/au...')
#53 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(1656): Tests\\Feature\\ApiIntegrationTest->test_taqnyat_otp_request_fails_without_user_phone()
#54 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ApiIntegrationTest))
#57 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/Framework/TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#59 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/src/TextUI/Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#60 /Users/<USER>/Herd/admin/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#61 {main}
"} 
[2025-06-30 00:23:24] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-30 00:23:24] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d8fc5550b","username":"a.younis"} 
[2025-06-30 00:23:24] testing.INFO: Taqnyat OTP request response {"status":200,"body":"otp:لقد تم ارسال رمز التحقق سابقا يمكن المحاوله بعد  52 ثانيه","headers":{"Date":["Mon, 30 Jun 2025 00:23:24 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=tih0iegohmbie2urf6014edt0v; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:23:24 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["105"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:23:24] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d8fc5550b","user_id":1} 
[2025-06-30 00:23:24] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-30 00:23:24] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d8fcd7976","username":"a.younis"} 
[2025-06-30 00:23:25] testing.INFO: Taqnyat OTP request response {"status":200,"body":"otp:لقد تم ارسال رمز التحقق سابقا يمكن المحاوله بعد  51 ثانيه","headers":{"Date":["Mon, 30 Jun 2025 00:23:25 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=3pu6lrc0ji1udnha6s36ed7ken; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:23:25 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["105"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:23:25] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d8fcd7976","user_id":1} 
[2025-06-30 00:23:25] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861d8fcd7976","user_id":1} 
[2025-06-30 00:23:25] testing.INFO: Taqnyat OTP verification initiated {"session_id":"taqnyat_otp_6861d8fcd7976","otp":"123456","username":"a.younis","attempts":1} 
[2025-06-30 00:23:25] testing.INFO: Taqnyat OTP verification - sending request with cookies {"cookies":"_clck=py1yak%7C2%7Cfwn%7C0%7C1953; ticketTypePay=8%2C13%2C21%2C24%2C27%2C32%2C35%2C39%2C46%2C47%2C51%2C66%2C70%2C80%2C82%2C84%2C86%2C88%2C90%2C92%2C94%2C96%2C98%2C100%2C102%2C104%2C106%2C107%2C109%2C111%2C113%2C114%2C116%2C118%2C120%2C122%2C124%2C126%2C130%2C132%2C134%2C136%2C138%2C140%2C142%2C144%2C146%2C148%2C152%2C154%2C156%2C158%2C160%2C162%2C164%2C166%2C168%2C170%2C172%2C174%2C176%2C178%2C181%2C183%2C185%2C187%2C189%2C191%2C193%2C195%2C197%2C199%2C201%2C203%2C205%2C207%2C209%2C211%2C213%2C214%2C216%2C218%2C220%2C222%2C224%2C226%2C244%2C246%2C248%2C250%2C252%2C254%2C256%2C258%2C260%2C262%2C264%2C266%2C268%2C270%2C; power=-; userNameT=a.younis; refreshPage=0; PHPSESSID=taqnyat_session_6861d8fd594d8","username":"a.younis","otp":"123456"} 
[2025-06-30 00:23:25] testing.INFO: Taqnyat OTP verification response {"status":200,"body":"otp:رمز التحقق غير صحيح","headers":{"Date":["Mon, 30 Jun 2025 00:23:25 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Last-Modified":["Mon, 30 Jun 2025 00:23:25 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["39"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:23:25] testing.INFO: Taqnyat OTP verification - analyzing response {"response_body":"otp:رمز التحقق غير صحيح","response_length":39,"otp_submitted":"123456"} 
[2025-06-30 00:23:25] testing.INFO: Taqnyat OTP verification - Demo mode activated {"otp":"123456","response_body":"otp:رمز التحقق غير صحيح"} 
[2025-06-30 00:23:25] testing.INFO: Taqnyat OTP verification successful {"session_id":"taqnyat_otp_6861d8fcd7976","phone_number":"+************","user_id":1} 
[2025-06-30 00:23:25] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-30 00:23:25] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:23:25] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:23:25] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:23:25] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:23:25] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:23:25] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:23:25] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:23:25] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:23:25] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:23:25] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-30 00:23:25] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-30 00:23:25] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-30 00:23:25] testing.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d8fde1254","username":"a.younis"} 
[2025-06-30 00:23:26] testing.INFO: Taqnyat OTP request response {"status":200,"body":"otp:لقد تم ارسال رمز التحقق سابقا يمكن المحاوله بعد  50 ثانيه","headers":{"Date":["Mon, 30 Jun 2025 00:23:26 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=kcfs99ja9qdc683p91o4427oah; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:23:26 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["105"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:23:26] testing.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d8fde1254","user_id":1} 
[2025-06-30 00:23:26] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-30 00:23:26] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-30 00:23:26] testing.INFO: 360Dialog token update initiated {"user_id":1,"token_length":1328} 
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT token validated and stored successfully {"token_length":1328} 
[2025-06-30 00:23:26] testing.INFO: 360Dialog token updated successfully {"user_id":1} 
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation: Production environment detected  
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation: Taqnyat organization detected  
[2025-06-30 00:23:26] testing.INFO: 360Dialog JWT validation successful {"algorithm":"RS256","issuer":"https://360dialog.eu.auth0.com/","organization":"taqnyat"} 
[2025-06-30 00:24:20] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","user_id":1,"user_email":"<EMAIL>"} 
[2025-06-30 00:24:20] local.INFO: Taqnyat OTP request initiated {"phone_number":"+************","session_id":"taqnyat_otp_6861d934a96a7","username":"a.younis"} 
[2025-06-30 00:24:21] local.INFO: Taqnyat OTP request response {"status":200,"body":"otp:تم ارسال رمز التحقق لرقم الجوال 7928********","headers":{"Date":["Mon, 30 Jun 2025 00:24:21 GMT"],"Server":[""],"Expires":["Mon, 26 Jul 1997 05:00:00 GMT"],"Cache-Control":["no-store, no-cache, must-revalidate, post-check=0, pre-check=0"],"Pragma":["no-cache"],"Set-Cookie":["PHPSESSID=81a79kkkn2dmkfgdak7im2dlp4; path=/; domain=cp.taqnyat.sa; secure; HttpOnly; SameSite=Lax"],"Last-Modified":["Mon, 30 Jun 2025 00:24:21 GMT"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["74"],"Keep-Alive":["timeout=3, max=150"],"Connection":["Keep-Alive"],"Content-Type":["text/html; charset=UTF-8"]}} 
[2025-06-30 00:24:21] local.INFO: Taqnyat OTP request successful {"phone_number":"+************","session_id":"taqnyat_otp_6861d934a96a7","user_id":1} 
