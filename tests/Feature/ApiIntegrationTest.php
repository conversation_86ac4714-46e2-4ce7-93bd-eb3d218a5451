<?php

namespace Tests\Feature;

use App\Models\User;
use App\Services\Api\TaqnyatService;
use App\Services\Api\Dialog360Service;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ApiIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $validJwtToken;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();

        // Valid JWT token for testing (based on the provided example)
        $this->validJwtToken = 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ik1EZEZOVFk1UVVVMU9FSXhPRGN3UVVZME9EUTFRVFJDT1RSRU9VUTVNVGhDTURWRk9UUTNPQSJ9.eyJodHRwczovL3J0LjM2MGRpYWxvZy5pby8iOnsiY3JlYXRlIjpmYWxzZSwiZW52aXJvbm1lbnQiOiJwcm9kdWN0aW9uIiwib3JnYW5pemF0aW9uIjoidGFxbnlhdCIsIm92ZXJzZWVyIjpmYWxzZSwidXNlcl9oYXNoIjoiMUJ1Z2xTVSJ9LCJpc3MiOiJodHRwczovLzM2MGRpYWxvZy5ldS5hdXRoMC5jb20vIiwic3ViIjoiYXV0aDB8NjFlNjgxM2RiZmFjOTgwMDY4MzkxZWFlIiwiYXVkIjpbImh0dHA6Ly8zNjBkaWFsb2cuaW8vc2VydmljZXMveHJheS9jcm0vdjEiLCJodHRwczovLzM2MGRpYWxvZy5ldS5hdXRoMC5jb20vdXNlcmluZm8iXSwiZXhwIjoxNjQyNjg0ODAwLCJpYXQiOjE2NDI1OTg0MDAsImF6cCI6IjM2MGRpYWxvZyJ9.signature_part_would_be_here_in_real_jwt_token_but_this_is_for_testing_purposes_only_and_contains_valid_base64url_characters';
    }

    public function test_taqnyat_otp_request_requires_authentication(): void
    {
        $response = $this->postJson('/api/taqnyat/auth/request-otp', [
            'phone_number' => '+************'
        ]);

        $response->assertStatus(401);
    }

    public function test_taqnyat_otp_request_fails_without_user_phone(): void
    {
        // Ensure user has no phone number
        $this->user->update(['phone' => null]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/taqnyat/auth/request-otp');

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'error'
            ])
            ->assertJson([
                'success' => false,
                'error' => 'VALIDATION_FAILED'
            ]);
    }

    public function test_taqnyat_otp_request_with_user_phone_number(): void
    {
        // Set phone number for user
        $this->user->update(['phone' => '+************']);

        $response = $this->actingAs($this->user)
            ->postJson('/api/taqnyat/auth/request-otp');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'session_id',
                'expires_in',
                'phone_number'
            ])
            ->assertJson([
                'success' => true,
                'phone_number' => '+************'
            ]);
    }

    public function test_taqnyat_otp_verification_requires_valid_session(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/taqnyat/auth/verify-otp', [
                'session_id' => 'invalid_session',
                'otp' => '123456'
            ]);

        $response->assertStatus(422); // Validation error for invalid session format
    }

    public function test_taqnyat_otp_verification_validates_otp_format(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/taqnyat/auth/verify-otp', [
                'session_id' => 'taqnyat_otp_test123',
                'otp' => '12345' // Invalid length
            ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'errors' => [
                    'otp'
                ]
            ]);
    }

    public function test_taqnyat_demo_otp_verification_works(): void
    {
        // Set phone number for user
        $this->user->update(['phone' => '+************']);

        // First request OTP
        $otpResponse = $this->actingAs($this->user)
            ->postJson('/api/taqnyat/auth/request-otp');

        $otpResponse->assertStatus(200);
        $sessionId = $otpResponse->json('session_id');

        // Then verify with demo OTP
        $verifyResponse = $this->actingAs($this->user)
            ->postJson('/api/taqnyat/auth/verify-otp', [
                'session_id' => $sessionId,
                'otp' => '123456'
            ]);

        $verifyResponse->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'access_token',
                'phone_number'
            ])
            ->assertJson([
                'success' => true,
                'phone_number' => '+************'
            ]);
    }

    public function test_taqnyat_auth_status_when_not_authenticated(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/taqnyat/auth/status');

        $response->assertStatus(200)
            ->assertJson([
                'authenticated' => false
            ]);
    }

    public function test_360dialog_token_update_requires_authentication(): void
    {
        $response = $this->postJson('/api/360dialog/auth/update-token', [
            'bearer_token' => $this->validJwtToken
        ]);

        $response->assertStatus(401);
    }

    public function test_360dialog_token_update_validates_token_format(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/360dialog/auth/update-token', [
                'bearer_token' => 'short' // Too short and not JWT format
            ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'errors' => [
                    'bearer_token'
                ]
            ]);
    }

    public function test_360dialog_token_update_with_valid_token(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/360dialog/auth/update-token', [
                'bearer_token' => $this->validJwtToken
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message'
            ])
            ->assertJson([
                'success' => true
            ]);
    }

    public function test_360dialog_auth_status_when_not_authenticated(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/360dialog/auth/status');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'authenticated',
                'status',
                'message'
            ])
            ->assertJson([
                'authenticated' => false
            ]);
    }

    public function test_360dialog_test_message_validates_phone_and_message(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/360dialog/send-test-message', [
                'to' => 'invalid_phone',
                'message' => ''
            ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'errors'
            ]);
    }

    public function test_taqnyat_service_can_be_instantiated(): void
    {
        $service = app(TaqnyatService::class);
        $this->assertInstanceOf(TaqnyatService::class, $service);
    }

    public function test_dialog360_service_can_be_instantiated(): void
    {
        $service = app(Dialog360Service::class);
        $this->assertInstanceOf(Dialog360Service::class, $service);
    }

    public function test_phone_number_formatting_in_requests(): void
    {
        // Set phone number for user first
        $this->user->update(['phone' => '+************']);

        // Test that OTP request works with user's phone number
        $response = $this->actingAs($this->user)
            ->postJson('/api/taqnyat/auth/request-otp');

        $response->assertStatus(200)
            ->assertJson([
                'phone_number' => '+************'
            ]);
    }

    public function test_bearer_token_validation_rejects_invalid_characters(): void
    {
        // Create a token that's long enough but has invalid characters
        $invalidToken = str_repeat('invalid@token#with$special%chars', 10); // Make it long enough

        $response = $this->actingAs($this->user)
            ->postJson('/api/360dialog/auth/update-token', [
                'bearer_token' => $invalidToken
            ]);

        $response->assertStatus(422)
            ->assertJsonStructure(['errors' => ['bearer_token']]);

        // Check that it contains a message about invalid characters
        $errors = $response->json('errors.bearer_token');
        $this->assertStringContainsString('invalid characters', $errors[0]);
    }

    public function test_otp_validation_rejects_non_numeric(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/taqnyat/auth/verify-otp', [
                'session_id' => 'taqnyat_otp_test123',
                'otp' => 'abc123'
            ]);

        $response->assertStatus(422)
            ->assertJsonPath('errors.otp.0', 'OTP must be exactly 6 digits');
    }

    public function test_user_can_update_phone_number(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/user/update-phone', [
                'phone' => '+************'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'phone'
            ])
            ->assertJson([
                'success' => true,
                'phone' => '+************'
            ]);

        // Verify the phone number was updated in the database
        $this->user->refresh();
        $this->assertEquals('+************', $this->user->phone);
    }

    public function test_phone_update_validates_format(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/user/update-phone', [
                'phone' => 'invalid-phone'
            ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'errors' => [
                    'phone'
                ]
            ]);
    }

    public function test_phone_update_prevents_duplicates(): void
    {
        // Create another user with a phone number
        $otherUser = User::factory()->create(['phone' => '+************']);

        $response = $this->actingAs($this->user)
            ->postJson('/api/user/update-phone', [
                'phone' => '+************'
            ]);

        $response->assertStatus(422)
            ->assertJsonPath('errors.phone.0', 'This phone number is already associated with another account');
    }

    public function test_jwt_token_validation_rejects_invalid_structure(): void
    {
        // Test token with only 2 parts (missing signature)
        $response = $this->actingAs($this->user)
            ->postJson('/api/360dialog/auth/update-token', [
                'bearer_token' => 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************'
            ]);

        $response->assertStatus(422)
            ->assertJsonStructure(['errors' => ['bearer_token']]);
    }

    public function test_jwt_token_validation_rejects_empty_parts(): void
    {
        // Test token with empty payload part
        $response = $this->actingAs($this->user)
            ->postJson('/api/360dialog/auth/update-token', [
                'bearer_token' => 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..signature'
            ]);

        $response->assertStatus(422)
            ->assertJsonStructure(['errors' => ['bearer_token']]);
    }

    public function test_jwt_token_validation_accepts_valid_jwt(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/360dialog/auth/update-token', [
                'bearer_token' => $this->validJwtToken
            ]);

        $response->assertStatus(200)
            ->assertJson(['success' => true]);
    }

    public function test_jwt_token_storage_and_retrieval(): void
    {
        // Store the JWT token
        $this->actingAs($this->user)
            ->postJson('/api/360dialog/auth/update-token', [
                'bearer_token' => $this->validJwtToken
            ]);

        // Check that the token can be retrieved and status shows authenticated
        $response = $this->actingAs($this->user)
            ->getJson('/api/360dialog/auth/status');

        $response->assertStatus(200)
            ->assertJson([
                'authenticated' => true,
                'status' => 'connected'
            ]);
    }
}
